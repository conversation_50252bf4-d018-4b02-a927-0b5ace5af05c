"""
Basic test to verify the test environment is working.
"""

import pytest
import sys
import os

# Add backend/src to Python path for testing
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend', 'src'))


def test_python_version():
    """Test that we're running on a supported Python version."""
    assert sys.version_info >= (3, 9), "Python 3.9+ is required"


def test_imports():
    """Test that basic imports work."""
    try:
        import flask
        import flask_sqlalchemy
        import email_validator
        assert True
    except ImportError as e:
        pytest.fail(f"Failed to import required packages: {e}")


def test_app_creation():
    """Test that the Flask app can be created."""
    try:
        from inquiry_system.core import create_app
        app = create_app('testing')
        assert app is not None

        # Test within app context to avoid SQLAlchemy issues
        with app.app_context():
            assert app.config['TESTING'] is True

    except Exception as e:
        pytest.fail(f"Failed to create Flask app: {e}")


def test_environment_variables():
    """Test that test environment variables are set."""
    assert os.environ.get('FLASK_ENV') == 'testing'
    assert os.environ.get('TESTING') == 'true'


if __name__ == '__main__':
    pytest.main([__file__])
