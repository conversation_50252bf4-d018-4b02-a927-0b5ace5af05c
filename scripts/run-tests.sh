#!/bin/bash
# Test runner script for Inquiry System v2.0

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
COVERAGE=false
VERBOSE=false
PATTERN=""
MARKERS=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --coverage)
            COVERAGE=true
            shift
            ;;
        --verbose|-v)
            VERBOSE=true
            shift
            ;;
        --pattern|-k)
            PATTERN="$2"
            shift 2
            ;;
        --markers|-m)
            MARKERS="$2"
            shift 2
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --coverage        Run tests with coverage report"
            echo "  --verbose, -v     Verbose output"
            echo "  --pattern, -k     Run tests matching pattern"
            echo "  --markers, -m     Run tests with specific markers"
            echo "  --help, -h        Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                           # Run all tests"
            echo "  $0 --coverage                # Run with coverage"
            echo "  $0 -k test_user              # Run tests matching 'test_user'"
            echo "  $0 -m 'not slow'             # Skip slow tests"
            echo "  $0 -m integration            # Run only integration tests"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Check and activate virtual environment
check_venv() {
    PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
    VENV_PATH="$PROJECT_ROOT/venv"

    # Check if virtual environment exists
    if [[ ! -d "$VENV_PATH" ]]; then
        print_error "Virtual environment not found at $VENV_PATH"
        print_error "Please run: ./scripts/run_dev.sh --setup"
        exit 1
    fi

    # Activate virtual environment if not already activated
    if [[ "$VIRTUAL_ENV" == "" ]]; then
        print_status "Activating virtual environment..."
        if [[ -f "$VENV_PATH/bin/activate" ]]; then
            source "$VENV_PATH/bin/activate"
        elif [[ -f "$VENV_PATH/Scripts/activate" ]]; then
            source "$VENV_PATH/Scripts/activate"
        else
            print_error "Could not find virtual environment activation script"
            exit 1
        fi
        print_success "Virtual environment activated"
    fi
}

# Check if pytest is available
check_pytest() {
    if ! command -v pytest &> /dev/null; then
        print_error "pytest not found. Please install it:"
        print_error "pip install pytest"
        exit 1
    fi
}

# Set up test environment
setup_test_env() {
    print_status "Setting up test environment..."
    
    # Set test environment variables
    export FLASK_ENV=testing
    export TESTING=true
    
    # Add backend/src to Python path
    export PYTHONPATH="${PWD}/backend/src:${PYTHONPATH}"
    
    print_success "Test environment configured"
}

# Run the tests
run_tests() {
    print_status "Running tests..."
    
    # Build pytest command
    PYTEST_CMD="pytest"
    
    # Add verbose flag if requested
    if [[ "$VERBOSE" == "true" ]]; then
        PYTEST_CMD="$PYTEST_CMD -v"
    fi
    
    # Add pattern if specified
    if [[ -n "$PATTERN" ]]; then
        PYTEST_CMD="$PYTEST_CMD -k '$PATTERN'"
    fi
    
    # Add markers if specified
    if [[ -n "$MARKERS" ]]; then
        PYTEST_CMD="$PYTEST_CMD -m '$MARKERS'"
    fi
    
    # Add coverage if requested
    if [[ "$COVERAGE" == "true" ]]; then
        PYTEST_CMD="$PYTEST_CMD --cov=inquiry_system --cov-report=term-missing --cov-report=html"
    fi
    
    # Add test directory
    PYTEST_CMD="$PYTEST_CMD tests/"
    
    print_status "Running: $PYTEST_CMD"
    
    # Execute the command
    if eval $PYTEST_CMD; then
        print_success "All tests passed!"
        
        if [[ "$COVERAGE" == "true" ]]; then
            print_success "Coverage report generated in htmlcov/"
        fi
        
        return 0
    else
        print_error "Some tests failed"
        return 1
    fi
}

# Generate test report
generate_report() {
    if [[ "$COVERAGE" == "true" ]]; then
        print_status "Generating test reports..."
        
        # Generate XML coverage report for CI
        if command -v coverage &> /dev/null; then
            coverage xml
            print_success "XML coverage report generated: coverage.xml"
        fi
        
        # Open HTML coverage report if available
        if [[ -f "htmlcov/index.html" ]]; then
            print_success "HTML coverage report: htmlcov/index.html"
            
            # Try to open in browser (optional)
            if command -v open &> /dev/null; then
                read -p "Open coverage report in browser? (y/N): " -n 1 -r
                echo
                if [[ $REPLY =~ ^[Yy]$ ]]; then
                    open htmlcov/index.html
                fi
            fi
        fi
    fi
}

# Main function
main() {
    echo "🧪 Running Inquiry System v2.0 Tests"
    echo "====================================="
    
    check_venv
    check_pytest
    setup_test_env
    
    if run_tests; then
        generate_report
        echo ""
        print_success "Test run completed successfully!"
        exit 0
    else
        echo ""
        print_error "Test run failed"
        echo ""
        echo "Tips for debugging:"
        echo "1. Run with --verbose for more details"
        echo "2. Run specific tests with --pattern"
        echo "3. Check test logs for error details"
        echo "4. Ensure test database is properly configured"
        exit 1
    fi
}

# Run main function
main "$@"
