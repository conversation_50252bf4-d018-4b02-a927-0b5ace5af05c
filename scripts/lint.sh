#!/bin/bash
# Code linting script for Inquiry System v2.0

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Track overall status
OVERALL_STATUS=0

# Check if we're in a virtual environment
check_venv() {
    if [[ "$VIRTUAL_ENV" == "" ]]; then
        print_warning "Virtual environment not detected"
        print_warning "Please activate your virtual environment first:"
        print_warning "source venv/bin/activate"
        exit 1
    fi
}

# Run flake8 linting
run_flake8() {
    print_status "Running flake8 linting..."
    
    if command -v flake8 &> /dev/null; then
        if flake8 backend/src/ tests/ scripts/ --max-line-length=88 --extend-ignore=E203,W503; then
            print_success "flake8 linting passed"
        else
            print_error "flake8 linting failed"
            OVERALL_STATUS=1
        fi
    else
        print_warning "flake8 not found. Skipping..."
    fi
}

# Run mypy type checking
run_mypy() {
    print_status "Running mypy type checking..."
    
    if command -v mypy &> /dev/null; then
        if mypy backend/src/ --ignore-missing-imports; then
            print_success "mypy type checking passed"
        else
            print_error "mypy type checking failed"
            OVERALL_STATUS=1
        fi
    else
        print_warning "mypy not found. Skipping..."
    fi
}

# Run bandit security scanning
run_bandit() {
    print_status "Running bandit security scanning..."
    
    if command -v bandit &> /dev/null; then
        if bandit -r backend/src/ -f json -o bandit-report.json; then
            print_success "bandit security scanning passed"
        else
            print_warning "bandit found potential security issues"
            print_warning "Check bandit-report.json for details"
        fi
    else
        print_warning "bandit not found. Skipping..."
    fi
}

# Run safety dependency scanning
run_safety() {
    print_status "Running safety dependency scanning..."
    
    if command -v safety &> /dev/null; then
        if safety check --json --output safety-report.json; then
            print_success "safety dependency scanning passed"
        else
            print_warning "safety found vulnerable dependencies"
            print_warning "Check safety-report.json for details"
        fi
    else
        print_warning "safety not found. Skipping..."
    fi
}

# Check import sorting
check_imports() {
    print_status "Checking import sorting with isort..."
    
    if command -v isort &> /dev/null; then
        if isort backend/src/ tests/ scripts/ --profile=black --check-only --diff; then
            print_success "Import sorting is correct"
        else
            print_error "Import sorting issues found"
            print_error "Run './scripts/format.sh' to fix"
            OVERALL_STATUS=1
        fi
    else
        print_warning "isort not found. Skipping..."
    fi
}

# Check code formatting
check_formatting() {
    print_status "Checking code formatting with black..."
    
    if command -v black &> /dev/null; then
        if black backend/src/ tests/ scripts/ --line-length=88 --check --diff; then
            print_success "Code formatting is correct"
        else
            print_error "Code formatting issues found"
            print_error "Run './scripts/format.sh' to fix"
            OVERALL_STATUS=1
        fi
    else
        print_warning "black not found. Skipping..."
    fi
}

# Main function
main() {
    echo "🔍 Linting Inquiry System v2.0 Code"
    echo "===================================="
    
    check_venv
    check_formatting
    check_imports
    run_flake8
    run_mypy
    run_bandit
    run_safety
    
    echo ""
    if [ $OVERALL_STATUS -eq 0 ]; then
        print_success "All linting checks passed!"
    else
        print_error "Some linting checks failed"
        echo ""
        echo "To fix formatting issues, run:"
        echo "  ./scripts/format.sh"
        echo ""
        echo "To see detailed reports, check:"
        echo "  bandit-report.json (security issues)"
        echo "  safety-report.json (dependency vulnerabilities)"
    fi
    
    exit $OVERALL_STATUS
}

# Run main function
main "$@"
