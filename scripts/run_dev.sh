#!/bin/bash
# Unified Development Environment Setup and Server Runner for Inquiry System v2.0
# This script combines setup-dev.sh and run-dev.py functionality with uv package management

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Project configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
VENV_PATH="$PROJECT_ROOT/venv"
PYTHON_MIN_VERSION="3.9"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# Function to check if uv is installed
check_uv() {
    if ! command -v uv &> /dev/null; then
        print_error "uv is not installed. Please install it first:"
        echo "  curl -LsSf https://astral.sh/uv/install.sh | sh"
        echo "  or visit: https://docs.astral.sh/uv/getting-started/installation/"
        exit 1
    fi
    print_success "uv found: $(uv --version)"
}

# Function to check Python version
check_python() {
    print_status "Checking Python version..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
        PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)
        
        if [ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -ge 9 ]; then
            print_success "Python $PYTHON_VERSION found"
            PYTHON_CMD="python3"
        else
            print_error "Python 3.9+ required, found $PYTHON_VERSION"
            exit 1
        fi
    else
        print_error "Python 3 not found. Please install Python 3.9 or higher."
        exit 1
    fi
}

# Function to create and setup virtual environment with uv
setup_venv() {
    print_status "Setting up virtual environment with uv..."
    
    cd "$PROJECT_ROOT"
    
    if [ ! -d "$VENV_PATH" ]; then
        print_status "Creating virtual environment..."
        uv venv "$VENV_PATH" --python "$PYTHON_CMD"
        print_success "Virtual environment created at $VENV_PATH"
    else
        print_warning "Virtual environment already exists at $VENV_PATH"
    fi
    
    # Activate virtual environment
    if [ -f "$VENV_PATH/bin/activate" ]; then
        source "$VENV_PATH/bin/activate"
        print_success "Virtual environment activated"
    elif [ -f "$VENV_PATH/Scripts/activate" ]; then
        source "$VENV_PATH/Scripts/activate"
        print_success "Virtual environment activated (Windows)"
    else
        print_error "Could not find virtual environment activation script"
        exit 1
    fi
}

# Function to install dependencies with uv
install_dependencies() {
    print_status "Installing dependencies with uv..."

    cd "$PROJECT_ROOT"

    # Check if we need to reinstall dependencies
    NEED_INSTALL=false

    if [ ! -f "$VENV_PATH/pyvenv.cfg" ]; then
        NEED_INSTALL=true
        print_status "Virtual environment is new, installing all dependencies..."
    elif [ "pyproject.toml" -nt "$VENV_PATH/pyvenv.cfg" ]; then
        NEED_INSTALL=true
        print_status "pyproject.toml is newer than virtual environment, updating dependencies..."
    elif [ "requirements.txt" -nt "$VENV_PATH/pyvenv.cfg" ]; then
        NEED_INSTALL=true
        print_status "requirements.txt is newer than virtual environment, updating dependencies..."
    fi

    if [ "$NEED_INSTALL" = true ]; then
        # Install the project in development mode with dev dependencies
        print_status "Installing project dependencies..."
        uv pip install -e ".[dev]"

        # Touch the pyvenv.cfg to update its timestamp
        touch "$VENV_PATH/pyvenv.cfg"

        print_success "All dependencies installed successfully"
    else
        print_success "Dependencies are up to date"
    fi
}

# Function to setup pre-commit hooks
setup_precommit() {
    print_status "Setting up pre-commit hooks..."
    
    if command -v pre-commit &> /dev/null; then
        pre-commit install
        print_success "Pre-commit hooks installed"
    else
        print_warning "pre-commit not available, skipping hook installation"
    fi
}

# Function to create environment file
setup_env() {
    print_status "Setting up environment configuration..."
    
    cd "$PROJECT_ROOT"
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_success "Environment file created from template"
            print_warning "Please edit .env file with your configuration"
        else
            print_warning ".env.example not found, creating basic .env file"
            cat > .env << EOF
FLASK_ENV=development
FLASK_DEBUG=1
FLASK_APP=inquiry_system.app:create_app
SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_urlsafe(32))")
DEV_DATABASE_URL=sqlite:///instance/inquiry_dev.db
LOG_LEVEL=DEBUG
FLASK_RUN_HOST=0.0.0.0
FLASK_RUN_PORT=5123
EOF
            print_success "Basic .env file created"
        fi
    else
        print_warning ".env file already exists"
    fi
}

# Function to create necessary directories
create_directories() {
    print_status "Creating instance directories..."
    
    cd "$PROJECT_ROOT"
    mkdir -p instance/{uploads,backups,logs}
    mkdir -p frontend/static/{css,js,images}
    mkdir -p tests/{unit,integration,fixtures}
    
    print_success "Directories created"
}

# Function to load environment variables
load_environment() {
    cd "$PROJECT_ROOT"
    if [ -f ".env" ]; then
        # Export variables from .env file, excluding readonly variables
        set -a
        # Use grep to exclude lines that might cause issues with readonly variables
        while IFS= read -r line; do
            # Skip comments, empty lines, and readonly variables
            if [[ ! "$line" =~ ^[[:space:]]*# ]] && [[ ! "$line" =~ ^[[:space:]]*$ ]] && [[ ! "$line" =~ ^[[:space:]]*UID= ]] && [[ ! "$line" =~ ^[[:space:]]*GID= ]]; then
                export "$line" 2>/dev/null || true
            fi
        done < .env
        set +a
        print_success "Environment variables loaded from .env"
    else
        print_warning ".env file not found, using default values"
    fi
}

# Function to check development environment
check_environment() {
    print_status "Checking development environment..."

    # Check if virtual environment exists
    if [ ! -d "$VENV_PATH" ]; then
        print_warning "Virtual environment not found at $VENV_PATH"
        return 1
    fi

    # Activate virtual environment for checking
    if [ -f "$VENV_PATH/bin/activate" ]; then
        source "$VENV_PATH/bin/activate"
    elif [ -f "$VENV_PATH/Scripts/activate" ]; then
        source "$VENV_PATH/Scripts/activate"
    else
        print_warning "Could not find virtual environment activation script"
        return 1
    fi

    # Check if .env file exists
    if [ ! -f "$PROJECT_ROOT/.env" ]; then
        print_warning ".env file not found"
        return 1
    fi

    # Check if required packages are installed
    if ! python -c "import flask, flask_sqlalchemy" 2>/dev/null; then
        print_warning "Missing Flask dependencies"
        return 1
    fi

    print_success "Development environment looks good!"
    return 0
}

# Function to initialize database
init_database() {
    print_status "Initializing development database..."
    
    cd "$PROJECT_ROOT"
    
    # Set PYTHONPATH to include backend/src
    export PYTHONPATH="${PROJECT_ROOT}/backend/src:${PYTHONPATH}"
    
    # Create database tables (this will be implemented later)
    # python -c "from inquiry_system.core import create_app, db; app = create_app('development'); app.app_context().push(); db.create_all()"
    
    print_success "Database initialization completed"
}

# Function to run the Flask development server
run_development_server() {
    print_header "🚀 Starting Inquiry System v2.0 Development Server..."

    cd "$PROJECT_ROOT"

    # Ensure virtual environment is activated
    if [ -f "$VENV_PATH/bin/activate" ]; then
        source "$VENV_PATH/bin/activate"
    elif [ -f "$VENV_PATH/Scripts/activate" ]; then
        source "$VENV_PATH/Scripts/activate"
    else
        print_error "Could not find virtual environment activation script"
        exit 1
    fi

    # Set PYTHONPATH to include backend/src
    export PYTHONPATH="${PROJECT_ROOT}/backend/src:${PYTHONPATH}"

    # Set development environment variables
    export FLASK_ENV="${FLASK_ENV:-development}"
    export FLASK_DEBUG="${FLASK_DEBUG:-1}"
    export FLASK_APP="${FLASK_APP:-inquiry_system.app:create_app}"

    # Get configuration
    HOST="${FLASK_RUN_HOST:-0.0.0.0}"
    PORT="${FLASK_RUN_PORT:-5123}"

    print_status "Server configuration:"
    echo "  🌐 Local:   http://localhost:$PORT"
    echo "  🌐 Network: http://$HOST:$PORT"
    echo "  📝 Log level: ${LOG_LEVEL:-DEBUG}"
    echo "  🗄️  Database: ${DEV_DATABASE_URL:-sqlite:///instance/inquiry_dev.db}"
    echo ""
    echo "Press Ctrl+C to stop the server"
    echo "=" * 50

    # Try to run with Flask directly for better debugging
    if python -c "from inquiry_system.core import create_app" 2>/dev/null; then
        python -c "
import os
import sys
sys.path.insert(0, '${PROJECT_ROOT}/backend/src')
from inquiry_system.core import create_app

app = create_app('development')
app.run(host='$HOST', port=$PORT, debug=True, use_reloader=True, use_debugger=True)
"
    else
        print_warning "Could not import application, falling back to flask run"
        flask run --host "$HOST" --port "$PORT" --debug
    fi
}

# Function to setup the development environment
setup_development_environment() {
    print_header "🚀 Setting up Inquiry System v2.0 Development Environment"
    echo "========================================================="
    
    check_uv
    check_python
    setup_venv
    install_dependencies
    setup_precommit
    setup_env
    create_directories
    init_database
    
    echo ""
    echo "========================================================="
    print_success "Development environment setup completed!"
    echo ""
    echo "Next steps:"
    echo "1. Edit .env file with your configuration (if needed)"
    echo "2. Run the development server: $0 --run"
    echo ""
    echo "Available commands:"
    echo "- $0 --setup          # Setup development environment"
    echo "- $0 --run            # Start development server"
    echo "- $0 --check          # Check environment status"
    echo ""
}

# Function to show usage information
show_usage() {
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Unified development environment setup and server runner for Inquiry System v2.0"
    echo ""
    echo "Options:"
    echo "  --setup, -s     Setup the development environment"
    echo "  --run, -r       Run the development server"
    echo "  --check, -c     Check environment status"
    echo "  --help, -h      Show this help message"
    echo ""
    echo "If no option is provided, the script will:"
    echo "1. Check if environment is set up"
    echo "2. Set up environment if needed"
    echo "3. Run the development server"
}

# Main function
main() {
    case "${1:-}" in
        --setup|-s)
            setup_development_environment
            ;;
        --run|-r)
            cd "$PROJECT_ROOT"
            load_environment
            if ! check_environment; then
                print_error "Environment not properly set up. Run: $0 --setup"
                exit 1
            fi
            run_development_server
            ;;
        --check|-c)
            cd "$PROJECT_ROOT"
            load_environment
            check_environment
            ;;
        --help|-h)
            show_usage
            ;;
        "")
            # Default behavior: setup if needed, then run
            cd "$PROJECT_ROOT"
            load_environment
            if ! check_environment; then
                print_status "Environment needs setup..."
                setup_development_environment
                echo ""
                print_status "Environment setup complete, starting server..."
                load_environment
            fi
            run_development_server
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
