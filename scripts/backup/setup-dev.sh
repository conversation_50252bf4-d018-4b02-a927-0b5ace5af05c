#!/bin/bash
# Development Environment Setup Script for Inquiry System v2.0

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python 3.9+ is available
check_python() {
    print_status "Checking Python version..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
        PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)
        
        if [ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -ge 9 ]; then
            print_success "Python $PYTHON_VERSION found"
            PYTHON_CMD="python3"
        else
            print_error "Python 3.9+ required, found $PYTHON_VERSION"
            exit 1
        fi
    else
        print_error "Python 3 not found. Please install Python 3.9 or higher."
        exit 1
    fi
}

# Create virtual environment
create_venv() {
    print_status "Creating virtual environment..."
    
    if [ ! -d "venv" ]; then
        $PYTHON_CMD -m venv venv
        print_success "Virtual environment created"
    else
        print_warning "Virtual environment already exists"
    fi
}

# Activate virtual environment
activate_venv() {
    print_status "Activating virtual environment..."
    
    if [ -f "venv/bin/activate" ]; then
        source venv/bin/activate
        print_success "Virtual environment activated"
    elif [ -f "venv/Scripts/activate" ]; then
        source venv/Scripts/activate
        print_success "Virtual environment activated (Windows)"
    else
        print_error "Could not find virtual environment activation script"
        exit 1
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Upgrade pip first
    pip install --upgrade pip
    
    # Install development dependencies
    if [ -f "requirements-dev.txt" ]; then
        pip install -r requirements-dev.txt
        print_success "Development dependencies installed"
    else
        print_warning "requirements-dev.txt not found, installing from pyproject.toml"
        pip install -e ".[dev]"
    fi
}

# Setup pre-commit hooks
setup_precommit() {
    print_status "Setting up pre-commit hooks..."
    
    if command -v pre-commit &> /dev/null; then
        pre-commit install
        print_success "Pre-commit hooks installed"
    else
        print_warning "pre-commit not available, skipping hook installation"
    fi
}

# Create environment file
setup_env() {
    print_status "Setting up environment configuration..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_success "Environment file created from template"
            print_warning "Please edit .env file with your configuration"
        else
            print_warning ".env.example not found, creating basic .env file"
            cat > .env << EOF
FLASK_ENV=development
FLASK_DEBUG=1
FLASK_APP=inquiry_system.app:create_app
SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_urlsafe(32))")
DEV_DATABASE_URL=sqlite:///instance/inquiry_dev.db
LOG_LEVEL=DEBUG
EOF
            print_success "Basic .env file created"
        fi
    else
        print_warning ".env file already exists"
    fi
}

# Create instance directories
create_directories() {
    print_status "Creating instance directories..."
    
    mkdir -p instance/{uploads,backups,logs}
    mkdir -p frontend/static/{css,js,images}
    mkdir -p tests/{unit,integration,fixtures}
    
    print_success "Directories created"
}

# Initialize database
init_database() {
    print_status "Initializing development database..."
    
    # Set PYTHONPATH to include backend/src
    export PYTHONPATH="${PWD}/backend/src:${PYTHONPATH}"
    
    # Create database tables (this will be implemented later)
    # python -c "from inquiry_system.core import create_app, db; app = create_app('development'); app.app_context().push(); db.create_all()"
    
    print_success "Database initialization completed"
}

# Main setup function
main() {
    echo "🚀 Setting up Inquiry System v2.0 Development Environment"
    echo "========================================================="
    
    check_python
    create_venv
    activate_venv
    install_dependencies
    setup_precommit
    setup_env
    create_directories
    init_database
    
    echo ""
    echo "========================================================="
    print_success "Development environment setup completed!"
    echo ""
    echo "Next steps:"
    echo "1. Activate the virtual environment: source venv/bin/activate"
    echo "2. Edit .env file with your configuration"
    echo "3. Run the development server: python scripts/run-dev.py"
    echo ""
    echo "Available commands:"
    echo "- scripts/run-dev.py          # Start development server"
    echo "- scripts/run-tests.sh        # Run test suite"
    echo "- scripts/lint.sh             # Run code quality checks"
    echo "- scripts/format.sh           # Format code"
    echo ""
}

# Run main function
main "$@"
