#!/usr/bin/env python3
"""
Development server runner for Inquiry System v2.0

This script provides a convenient way to run the development server
with proper configuration and hot reloading.
"""

import os
import sys
import subprocess
from pathlib import Path

# Add backend/src to Python path
project_root = Path(__file__).parent.parent
backend_src = project_root / "backend" / "src"
sys.path.insert(0, str(backend_src))

def check_environment():
    """Check if the development environment is properly set up."""
    print("🔍 Checking development environment...")
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Virtual environment not detected")
        print("   Please activate your virtual environment first:")
        print("   source venv/bin/activate")
        return False
    
    # Check if .env file exists
    env_file = project_root / ".env"
    if not env_file.exists():
        print("⚠️  Warning: .env file not found")
        print("   Please copy .env.example to .env and configure it")
        return False
    
    # Check if required packages are installed
    try:
        import flask
        import flask_sqlalchemy
        print("✅ Flask dependencies found")
    except ImportError as e:
        print(f"❌ Missing dependencies: {e}")
        print("   Please run: pip install -r requirements-dev.txt")
        return False
    
    print("✅ Development environment looks good!")
    return True

def load_environment():
    """Load environment variables from .env file."""
    try:
        from dotenv import load_dotenv
        env_file = project_root / ".env"
        load_dotenv(env_file)
        print(f"📁 Loaded environment from {env_file}")
    except ImportError:
        print("⚠️  python-dotenv not installed, using system environment")
    except Exception as e:
        print(f"⚠️  Could not load .env file: {e}")

def run_development_server():
    """Run the Flask development server."""
    print("🚀 Starting Inquiry System v2.0 Development Server...")
    
    # Set development environment variables
    os.environ.setdefault('FLASK_ENV', 'development')
    os.environ.setdefault('FLASK_DEBUG', '1')
    os.environ.setdefault('FLASK_APP', 'inquiry_system.app:create_app')
    
    # Get configuration
    host = os.environ.get('FLASK_RUN_HOST', '0.0.0.0')
    port = int(os.environ.get('FLASK_RUN_PORT', 5123))
    
    try:
        # Try to import and run the app directly for better debugging
        from inquiry_system.core import create_app
        
        app = create_app('development')
        
        print(f"🌐 Server will be available at:")
        print(f"   Local:   http://localhost:{port}")
        print(f"   Network: http://{host}:{port}")
        print(f"📝 Log level: {os.environ.get('LOG_LEVEL', 'DEBUG')}")
        print(f"🗄️  Database: {app.config.get('SQLALCHEMY_DATABASE_URI', 'Not configured')}")
        print("")
        print("Press Ctrl+C to stop the server")
        print("=" * 50)
        
        app.run(
            host=host,
            port=port,
            debug=True,
            use_reloader=True,
            use_debugger=True
        )
        
    except ImportError:
        print("⚠️  Could not import application, falling back to flask run")
        # Fallback to flask run command
        cmd = [
            sys.executable, '-m', 'flask', 'run',
            '--host', host,
            '--port', str(port),
            '--debug'
        ]
        subprocess.run(cmd)
    
    except KeyboardInterrupt:
        print("\n👋 Development server stopped")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

def main():
    """Main entry point."""
    print("=" * 60)
    print("🔧 Inquiry System v2.0 - Development Server")
    print("=" * 60)
    
    # Change to project root directory
    os.chdir(project_root)
    
    # Load environment variables
    load_environment()
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment check failed. Please fix the issues above.")
        sys.exit(1)
    
    # Run the server
    run_development_server()

if __name__ == '__main__':
    main()
