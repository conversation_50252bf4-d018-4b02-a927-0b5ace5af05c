#!/bin/bash
# Code formatting script for Inquiry System v2.0

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in a virtual environment
check_venv() {
    if [[ "$VIRTUAL_ENV" == "" ]]; then
        print_warning "Virtual environment not detected"
        print_warning "Please activate your virtual environment first:"
        print_warning "source venv/bin/activate"
        exit 1
    fi
}

# Format Python code with Black
format_python() {
    print_status "Formatting Python code with Black..."
    
    if command -v black &> /dev/null; then
        black backend/src/ tests/ scripts/ --line-length=88
        print_success "Python code formatted with Black"
    else
        print_error "Black not found. Please install it: pip install black"
        exit 1
    fi
}

# Sort imports with isort
sort_imports() {
    print_status "Sorting imports with isort..."
    
    if command -v isort &> /dev/null; then
        isort backend/src/ tests/ scripts/ --profile=black
        print_success "Imports sorted with isort"
    else
        print_error "isort not found. Please install it: pip install isort"
        exit 1
    fi
}

# Format YAML files
format_yaml() {
    print_status "Checking YAML files..."
    
    # Find and format YAML files
    find . -name "*.yml" -o -name "*.yaml" | while read -r file; do
        if [[ "$file" != *"/.git/"* ]] && [[ "$file" != *"/venv/"* ]]; then
            print_status "Checking $file"
            # Basic YAML validation
            python3 -c "import yaml; yaml.safe_load(open('$file'))" 2>/dev/null || {
                print_warning "YAML syntax error in $file"
            }
        fi
    done
    
    print_success "YAML files checked"
}

# Format JSON files
format_json() {
    print_status "Formatting JSON files..."
    
    # Find and format JSON files
    find . -name "*.json" | while read -r file; do
        if [[ "$file" != *"/.git/"* ]] && [[ "$file" != *"/venv/"* ]] && [[ "$file" != *"/node_modules/"* ]]; then
            print_status "Formatting $file"
            python3 -m json.tool "$file" > "${file}.tmp" && mv "${file}.tmp" "$file" || {
                print_warning "JSON syntax error in $file"
                rm -f "${file}.tmp"
            }
        fi
    done
    
    print_success "JSON files formatted"
}

# Main function
main() {
    echo "🎨 Formatting Inquiry System v2.0 Code"
    echo "======================================"
    
    check_venv
    format_python
    sort_imports
    format_yaml
    format_json
    
    echo ""
    print_success "Code formatting completed!"
    echo ""
    echo "Next steps:"
    echo "1. Review the changes: git diff"
    echo "2. Run linting: ./scripts/lint.sh"
    echo "3. Run tests: ./scripts/run-tests.sh"
}

# Run main function
main "$@"
