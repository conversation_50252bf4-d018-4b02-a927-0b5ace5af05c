# Inquiry System v2.0

A modern, scalable Flask application for product inquiry management with comprehensive supplier tracking, order management, and inventory control.

## 🚀 Features

- **Modern Architecture**: Clean separation of concerns with backend/frontend structure
- **RESTful API**: Versioned API with comprehensive endpoints
- **Authentication & Authorization**: Secure user management with role-based access
- **Product Inquiry Management**: Track customer inquiries and supplier quotes
- **Order Management**: Complete order lifecycle management
- **Inventory Tracking**: Real-time inventory management with serial number tracking
- **Audit Logging**: Comprehensive activity logging for compliance
- **Multi-Environment Support**: Development, testing, and production configurations
- **Docker Support**: Containerized deployment with Docker Compose
- **Code Quality**: Pre-commit hooks, linting, and automated testing

## 📁 Project Structure

```
inquiry-system/
├── app.py                      # Main application entry point
├── backend/                    # Backend application
│   ├── src/
│   │   └── inquiry_system/
│   │       ├── api/           # API endpoints
│   │       ├── config/        # Configuration management
│   │       ├── core/          # Core application components
│   │       ├── frontend/      # Frontend blueprint
│   │       ├── models/        # Database models
│   │       └── utils/         # Utility functions
│   └── tests/                 # Test suite
├── frontend/                   # Frontend assets
│   ├── templates/             # Jinja2 templates
│   └── static/               # CSS, JS, images
├── backup_legacy/             # Legacy code backup
├── scripts/                   # Development scripts
├── docker/                    # Docker configuration
├── docs/                      # Documentation
└── deployment/               # Deployment configurations
```

## 🛠️ Quick Start

### Prerequisites

- Python 3.9 or higher
- Node.js 16+ (for frontend dependencies)
- PostgreSQL (for production) or SQLite (for development)
- Redis (optional, for caching and rate limiting)

### Development Setup

1. **Clone and setup the project:**
```bash
git clone <repository-url>
cd inquiry-system
chmod +x scripts/setup-dev.sh
./scripts/setup-dev.sh
```

2. **Activate virtual environment:**
```bash
source venv/bin/activate
```

3. **Configure environment:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Run the development server:**
```bash
python scripts/run-dev.py
```

The application will be available at http://localhost:5123

### Docker Setup

1. **Using Docker Compose:**
```bash
docker-compose up -d
```

2. **For development with hot reload:**
```bash
docker-compose -f docker-compose.dev.yml up
```

## 🔧 Development

### Available Scripts

- `scripts/setup-dev.sh` - Set up development environment
- `scripts/run-dev.py` - Start development server
- `scripts/format.sh` - Format code with Black and isort
- `scripts/lint.sh` - Run code quality checks
- `scripts/run-tests.sh` - Run test suite

### Code Quality

This project uses several tools to maintain code quality:

- **Black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting
- **mypy**: Type checking
- **bandit**: Security scanning
- **safety**: Dependency vulnerability scanning
- **pre-commit**: Git hooks for automated checks

### Testing

```bash
# Run all tests
./scripts/run-tests.sh

# Run specific test categories
pytest tests/unit/
pytest tests/integration/
pytest tests/ -m "not slow"

# Run with coverage
pytest --cov=inquiry_system --cov-report=html
```

### Database Management

```bash
# Initialize database
flask init-db

# Create a new user
flask create-user --username admin --email <EMAIL> --admin

# Reset database (WARNING: Deletes all data)
flask reset-db
```

## 🏗️ Architecture

### Backend Architecture

The backend follows a layered architecture pattern:

- **API Layer**: RESTful endpoints with versioning
- **Service Layer**: Business logic and data processing
- **Model Layer**: Database models and relationships
- **Core Layer**: Application configuration and extensions

### Database Schema

Key entities and relationships:

- **Users**: Authentication and authorization
- **Customers**: Customer information and contacts
- **Product Inquiries**: Customer product requests
- **Suppliers**: Supplier information and contacts
- **Supplier Quotes**: Price quotes from suppliers
- **Orders**: Customer orders and fulfillment
- **Inventory**: Stock management and tracking

### API Design

The API follows RESTful principles with:

- Versioned endpoints (`/api/v1/`)
- Consistent response formats
- Proper HTTP status codes
- Comprehensive error handling
- Rate limiting and authentication

## 🚀 Deployment

### Production Deployment

1. **Environment Configuration:**
```bash
cp .env.example .env.production
# Configure production settings
```

2. **Database Setup:**
```bash
# PostgreSQL setup
createdb inquiry_prod
flask db upgrade
```

3. **Using Docker:**
```bash
docker-compose -f docker-compose.yml up -d
```

4. **Using Gunicorn:**
```bash
pip install -r requirements-prod.txt
gunicorn "inquiry_system.core:create_app('production')" \
    --bind 0.0.0.0:8000 \
    --workers 4 \
    --worker-class gevent
```

### Environment Variables

Key environment variables for production:

```bash
FLASK_ENV=production
SECRET_KEY=your-secure-secret-key
DATABASE_URL=postgresql://user:pass@localhost/inquiry_prod
REDIS_URL=redis://localhost:6379/0
SENTRY_DSN=your-sentry-dsn
```

## 📊 Monitoring

### Health Checks

- `/api/v1/health/` - Basic health check
- `/api/v1/health/detailed` - Detailed component status
- `/api/v1/health/ready` - Kubernetes readiness probe
- `/api/v1/health/live` - Kubernetes liveness probe

### Logging

Structured logging with different levels:

- **DEBUG**: Development debugging
- **INFO**: General information
- **WARNING**: Warning conditions
- **ERROR**: Error conditions
- **CRITICAL**: Critical errors

### Metrics

Integration with Prometheus for metrics collection:

- Request/response metrics
- Database query performance
- Custom business metrics
- System resource usage

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Install pre-commit hooks**: `pre-commit install`
4. **Make your changes and commit**: `git commit -m 'Add amazing feature'`
5. **Push to the branch**: `git push origin feature/amazing-feature`
6. **Open a Pull Request**

### Development Guidelines

- Follow PEP 8 style guidelines
- Write comprehensive tests
- Update documentation
- Use meaningful commit messages
- Ensure all checks pass

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/inquiry-system/inquiry-system/issues)
- **Discussions**: [GitHub Discussions](https://github.com/inquiry-system/inquiry-system/discussions)

## 🔄 Migration from v1.0

If you're upgrading from the previous version:

1. **Backup your data**
2. **Review the migration guide**: [docs/migration.md](docs/migration.md)
3. **Update your configuration**
4. **Run database migrations**
5. **Test thoroughly**

---

**Built with ❤️ by the Inquiry System Team**