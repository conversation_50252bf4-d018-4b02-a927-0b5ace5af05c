{"name": "product-inquiry-system", "version": "1.0.0", "description": "Product Inquiry Management System with dynamic serial number fields and responsive quote buttons", "main": "app.py", "scripts": {"start": "./quick_start.sh", "setup": "./start_service.sh", "dev": "./quick_start.sh", "install-deps": "./start_service.sh --install-only", "reset-db": "./start_service.sh --reset-db"}, "keywords": ["flask", "product-inquiry", "inventory", "quotes", "serial-numbers"], "author": "Product Inquiry System Team", "license": "MIT", "devDependencies": {"bootstrap": "^5.3.0", "bootstrap-icons": "^1.10.0"}, "engines": {"node": ">=16.0.0", "python": ">=3.9.0"}, "repository": {"type": "git", "url": "."}, "bugs": {"url": "."}, "homepage": "."}