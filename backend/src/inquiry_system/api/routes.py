"""
API routes for the Inquiry System.

This module contains all API endpoints without versioning.
"""

import json
import os
from datetime import datetime, date
from flask import Blueprint, request, jsonify, render_template, redirect, url_for, current_app
from flask_login import login_required, current_user, login_user, logout_user
from sqlalchemy import func, or_
from werkzeug.security import check_password_hash

from inquiry_system.core.database import db
from inquiry_system.models import (
    Customer, ProductInquiry, SupplierQuote, Supplier, User, UserActivityLog,
    SerialNumber, Order, Inventory, InventorySerialNumber
)

# Create the main API blueprint
api_bp = Blueprint('api', __name__)

# ----------------------
# Health Check
# ----------------------

@api_bp.route('/health')
def health_check():
    """Health check endpoint for Docker health checks"""
    try:
        # Test database connection
        from sqlalchemy import text
        db.session.execute(text('SELECT 1'))
        return jsonify({
            'status': 'healthy',
            'database': 'connected',
            'timestamp': datetime.now().isoformat()
        }), 200
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'database': 'disconnected',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 503

# ----------------------
# Authentication
# ----------------------

@api_bp.route('/auth/login', methods=['POST'])
def login():
    """User login endpoint"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400
        
        username = data.get('username', '').strip()
        password = data.get('password', '')
        
        if not username:
            return jsonify({'error': 'Username is required'}), 400
        if not password:
            return jsonify({'error': 'Password is required'}), 400
        
        # Find user by username or email
        user = User.query.filter(
            (User.username == username) | (User.email == username)
        ).first()
        
        if not user or not user.check_password(password):
            # Log failed login attempt
            if user:
                UserActivityLog.log_activity(
                    user_id=user.id,
                    action_type="login_failed",
                    description="Failed login attempt",
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent'),
                    endpoint=request.endpoint,
                    method=request.method
                )
            return jsonify({'error': 'Invalid username or password'}), 401
        
        if not user.is_active:
            return jsonify({'error': 'Account is disabled'}), 401
        
        # Log successful login
        login_user(user)
        user.update_last_login()
        
        UserActivityLog.log_activity(
            user_id=user.id,
            action_type="login_success",
            description="Successful login",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent'),
            endpoint=request.endpoint,
            method=request.method,
            status_code=200
        )
        
        return jsonify({
            'message': 'Login successful',
            'user': user.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'Login error: {e}')
        return jsonify({'error': 'Internal server error'}), 500

@api_bp.route('/auth/logout', methods=['POST'])
@login_required
def logout():
    """User logout endpoint"""
    try:
        # Log logout activity
        UserActivityLog.log_activity(
            user_id=current_user.id,
            action_type="logout",
            description="User logout",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent'),
            endpoint=request.endpoint,
            method=request.method,
            status_code=200
        )
        
        logout_user()
        return jsonify({'message': 'Logout successful'}), 200
        
    except Exception as e:
        current_app.logger.error(f'Logout error: {e}')
        return jsonify({'error': 'Internal server error'}), 500

# ----------------------
# Customer Management
# ----------------------

# Country list (Chinese-English mapping)
COUNTRIES = {
    'USA': '美国',
    'JAPAN': '日本',
    'KOREA': '韩国',
}

@api_bp.route('/customers', methods=['GET'])
@login_required
def get_customers():
    try:
        customers = Customer.query.order_by(Customer.name).all()
        return jsonify([{
            'id': customer.id,
            'name': customer.name,
            'address': customer.address,
            'phone': customer.phone,
            'contact': customer.contact,
            'country': customer.country,
            'country_name': COUNTRIES.get(customer.country, customer.country)
        } for customer in customers])
    except Exception as e:
        current_app.logger.error(f"Error getting customers: {str(e)}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/customers/countries', methods=['GET'])
@login_required
def get_countries():
    return jsonify([{
        'code': code,
        'name': name
    } for code, name in COUNTRIES.items()])

@api_bp.route('/customers', methods=['POST'])
@login_required
def create_customer():
    data = request.get_json()

    # Validate all required fields
    required_fields = {
        'name': '客户名称',
        'address': '地址',
        'phone': '电话',
        'contact': '联系人',
        'country': '国家'
    }

    for field, field_name in required_fields.items():
        if not data.get(field):
            return jsonify({'error': f'{field_name}不能为空'}), 400

    try:
        customer = Customer(
            name=data['name'],
            address=data['address'],
            phone=data['phone'],
            contact=data['contact'],
            country=data['country']
        )
        db.session.add(customer)
        db.session.commit()

        # Add audit record
        log = UserActivityLog(
            user_id=current_user.id,
            action_type="新增客户",
            details=json.dumps({
                "customer_id": customer.id,
                "name": customer.name,
                "country": customer.country
            }),
            ip_address=request.remote_addr
        )
        db.session.add(log)
        db.session.commit()

        return jsonify({
            'id': customer.id,
            'name': customer.name,
            'address': customer.address,
            'phone': customer.phone,
            'contact': customer.contact,
            'country': customer.country,
            'country_name': COUNTRIES.get(customer.country, customer.country)
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating customer: {str(e)}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/customers/<int:customer_id>', methods=['PUT'])
@login_required
def update_customer(customer_id):
    customer = Customer.query.get_or_404(customer_id)
    data = request.get_json()

    # Validate all required fields
    required_fields = {
        'name': '客户名称',
        'address': '地址',
        'phone': '电话',
        'contact': '联系人',
        'country': '国家'
    }

    for field, field_name in required_fields.items():
        if field in data and not data[field]:
            return jsonify({'error': f'{field_name}不能为空'}), 400

    try:
        if 'name' in data:
            customer.name = data['name']
        if 'address' in data:
            customer.address = data['address']
        if 'phone' in data:
            customer.phone = data['phone']
        if 'contact' in data:
            customer.contact = data['contact']
        if 'country' in data:
            customer.country = data['country']

        db.session.commit()

        return jsonify({
            'id': customer.id,
            'name': customer.name,
            'address': customer.address,
            'phone': customer.phone,
            'contact': customer.contact,
            'country': customer.country,
            'country_name': COUNTRIES.get(customer.country, customer.country)
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating customer: {str(e)}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/customers/<int:customer_id>', methods=['DELETE'])
@login_required
def delete_customer(customer_id):
    customer = Customer.query.get_or_404(customer_id)

    try:
        db.session.delete(customer)
        db.session.commit()
        return jsonify({'message': '客户已删除'})
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting customer: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ----------------------
# Product Search
# ----------------------

@api_bp.route('/products/search')
@login_required
def search_products():
    query = request.args.get('query', '').strip().lower()
    customer_id = request.args.get('customer_id')
    
    if not query:
        return jsonify([])
    
    try:
        # Build base query
        base_query = db.session.query(ProductInquiry).join(Customer)
        
        # Add search conditions (case insensitive)
        search_conditions = [
            func.lower(ProductInquiry.product_name).like(f'%{query}%'),
            func.lower(ProductInquiry.brand).like(f'%{query}%')
        ]
        
        # If customer ID is specified, add customer filter
        if customer_id:
            base_query = base_query.filter(ProductInquiry.customer_id == customer_id)
        
        # Execute query
        results = base_query.filter(or_(*search_conditions))\
            .order_by(ProductInquiry.inquiry_date.desc())\
            .limit(10)\
            .all()
        
        # Convert results to JSON
        products = []
        for inquiry in results:
            product = {
                'product_name': inquiry.product_name,
                'brand': inquiry.brand,
                'quantity': inquiry.quantity,
                'expected_delivery_days': inquiry.expected_delivery_days,
                'my_price': inquiry.my_price,
                'inquiry_date': inquiry.inquiry_date.isoformat(),
                'customer_id': inquiry.customer_id,
                'customer_name': inquiry.customer.name,
                'supplier_quotes': []
            }
            
            # Add supplier quotes
            for quote in inquiry.supplier_quotes:
                product['supplier_quotes'].append({
                    'supplier_name': quote.supplier.name,
                    'supplier_contact': quote.supplier.contact,
                    'price': float(quote.price),
                    'delivery_days': quote.delivery_days
                })
            
            products.append(product)
        
        return jsonify(products)
        
    except Exception as e:
        current_app.logger.error(f"Search error: {str(e)}")
        return jsonify([])

# ----------------------
# Supplier Management
# ----------------------

@api_bp.route('/suppliers', methods=['GET'])
@login_required
def get_suppliers():
    try:
        suppliers = Supplier.query.order_by(Supplier.name).all()
        return jsonify([{
            'id': supplier.id,
            'name': supplier.name,
            'contact': supplier.contact,
            'phone': supplier.phone,
            'email': supplier.email,
            'address': supplier.address,
            'country': supplier.country
        } for supplier in suppliers])
    except Exception as e:
        current_app.logger.error(f"Error getting suppliers: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ----------------------
# Inquiry Management
# ----------------------

@api_bp.route('/inquiries', methods=['POST'])
@login_required
def create_inquiry():
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400

    required_fields = ['product_name', 'customer_id', 'inquiry_date', 'quantity']
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        return jsonify({"error": f"Missing required fields: {', '.join(missing_fields)}"}), 400

    # Validate customer exists
    customer = Customer.query.get(data['customer_id'])
    if not customer:
        return jsonify({"error": f"Customer with id {data['customer_id']} not found"}), 400

    try:
        # Parse date string
        inquiry_date = datetime.strptime(data['inquiry_date'], '%Y-%m-%d').date()

        # Check if same customer has same product record
        existing_inquiry = ProductInquiry.query.filter_by(
            customer_id=data['customer_id'],
            product_name=data['product_name']
        ).first()

        if existing_inquiry:
            # Update existing record
            existing_inquiry.brand = data.get('brand', '')
            existing_inquiry.quantity = int(data['quantity'])
            existing_inquiry.inquiry_date = inquiry_date

            # Delete existing supplier quotes
            SupplierQuote.query.filter_by(inquiry_id=existing_inquiry.id).delete()

            # Add new supplier quotes
            for quote in data.get('supplier_quotes', []):
                # Find or create supplier
                supplier = Supplier.query.filter_by(name=quote['supplier_name']).first()
                if not supplier:
                    supplier = Supplier(
                        name=quote['supplier_name'],
                        contact=quote.get('contact', ''),
                        default_delivery_days=int(quote.get('delivery_days', 7))
                    )
                    db.session.add(supplier)
                    db.session.flush()  # Get supplier.id

                supplier_quote = SupplierQuote(
                    inquiry_id=existing_inquiry.id,
                    supplier_id=supplier.id,
                    product_name=data['product_name'],
                    brand=data.get('brand', ''),
                    quantity=int(data['quantity']),
                    price=float(quote.get('price', quote.get('unit_price', 0))),
                    my_price=float(data.get('my_price', 0)),
                    delivery_days=int(quote.get('delivery_days', 7)),
                    inquiry_date=inquiry_date
                )
                db.session.add(supplier_quote)

            inquiry_id = existing_inquiry.id
        else:
            # Create new inquiry record
            new_inquiry = ProductInquiry(
                customer_id=data['customer_id'],
                brand=data.get('brand', ''),
                product_name=data['product_name'],
                quantity=int(data['quantity']),
                expected_delivery_days=int(data.get('expected_delivery_days', 7)),
                my_price=float(data.get('my_price', 0)),
                inquiry_date=inquiry_date
            )
            db.session.add(new_inquiry)
            db.session.flush()  # Get new record ID

            # Add supplier quotes
            for quote in data.get('supplier_quotes', []):
                # Find or create supplier
                supplier = Supplier.query.filter_by(name=quote['supplier_name']).first()
                if not supplier:
                    supplier = Supplier(
                        name=quote['supplier_name'],
                        contact=quote.get('contact', ''),
                        default_delivery_days=int(quote.get('delivery_days', 7))
                    )
                    db.session.add(supplier)
                    db.session.flush()  # Get supplier.id

                supplier_quote = SupplierQuote(
                    inquiry_id=new_inquiry.id,
                    supplier_id=supplier.id,
                    product_name=data['product_name'],
                    brand=data.get('brand', ''),
                    quantity=int(data['quantity']),
                    price=float(quote.get('price', quote.get('unit_price', 0))),
                    my_price=float(data.get('my_price', 0)),
                    delivery_days=int(quote.get('delivery_days', 7)),
                    inquiry_date=inquiry_date
                )
                db.session.add(supplier_quote)

            inquiry_id = new_inquiry.id

        db.session.commit()
        return jsonify({
            "message": "Inquiry saved",
            "id": inquiry_id
        }), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error saving inquiry: {str(e)}")
        return jsonify({"error": f"Failed to save inquiry: {str(e)}"}), 500

@api_bp.route('/inquiries', methods=['GET'])
@login_required
def get_inquiries():
    """Get all inquiries with optional filtering"""
    try:
        customer_id = request.args.get('customer_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        query = ProductInquiry.query.join(Customer)

        if customer_id:
            query = query.filter(ProductInquiry.customer_id == customer_id)

        if start_date:
            query = query.filter(ProductInquiry.inquiry_date >= start_date)

        if end_date:
            query = query.filter(ProductInquiry.inquiry_date <= end_date)

        inquiries = query.order_by(ProductInquiry.inquiry_date.desc()).all()

        result = []
        for inquiry in inquiries:
            inquiry_data = {
                'id': inquiry.id,
                'customer_id': inquiry.customer_id,
                'customer_name': inquiry.customer.name,
                'brand': inquiry.brand,
                'product_name': inquiry.product_name,
                'quantity': inquiry.quantity,
                'expected_delivery_days': inquiry.expected_delivery_days,
                'my_price': inquiry.my_price,
                'inquiry_date': inquiry.inquiry_date.isoformat(),
                'supplier_quotes': []
            }

            for quote in inquiry.supplier_quotes:
                inquiry_data['supplier_quotes'].append({
                    'id': quote.id,
                    'supplier_id': quote.supplier_id,
                    'supplier_name': quote.supplier.name,
                    'price': quote.price,
                    'my_price': quote.my_price,
                    'delivery_days': quote.delivery_days,
                    'quantity': quote.quantity
                })

            result.append(inquiry_data)

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"Error getting inquiries: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ----------------------
# Order Management
# ----------------------

@api_bp.route('/orders', methods=['POST'])
@login_required
def create_order():
    """Create a new order from supplier quote"""
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400

    supplier_quote_id = data.get('supplier_quote_id')
    if not supplier_quote_id:
        return jsonify({"error": "supplier_quote_id is required"}), 400

    try:
        # Get the supplier quote
        supplier_quote = SupplierQuote.query.get(supplier_quote_id)
        if not supplier_quote:
            return jsonify({"error": "Supplier quote not found"}), 404

        # Create new order
        order = Order(
            supplier_quote=supplier_quote,
            inquiry_id=supplier_quote.inquiry_id,
            supplier_quote_id=supplier_quote_id,
            notes=data.get('notes', '')
        )

        db.session.add(order)
        db.session.commit()

        return jsonify({
            "message": "Order created successfully",
            "order_id": order.id
        }), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating order: {str(e)}")
        return jsonify({"error": f"Failed to create order: {str(e)}"}), 500

@api_bp.route('/orders', methods=['GET'])
@login_required
def get_orders():
    """Get all orders with optional filtering"""
    try:
        customer_id = request.args.get('customer_id')
        status = request.args.get('status')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        query = Order.query.join(Customer)

        if customer_id:
            query = query.filter(Order.customer_id == customer_id)

        if status:
            query = query.filter(Order.status == status)

        if start_date:
            query = query.filter(Order.order_date >= start_date)

        if end_date:
            query = query.filter(Order.order_date <= end_date)

        orders = query.order_by(Order.order_date.desc()).all()

        result = []
        for order in orders:
            result.append({
                'id': order.id,
                'customer_id': order.customer_id,
                'customer_name': order.customer.name,
                'product_name': order.product_name,
                'brand': order.brand,
                'quantity': order.quantity,
                'price': order.price,
                'my_price': order.my_price,
                'supplier_name': order.supplier_name,
                'delivery_days': order.delivery_days,
                'order_date': order.order_date.isoformat(),
                'expected_delivery_date': order.expected_delivery_date.isoformat() if order.expected_delivery_date else None,
                'status': order.status,
                'notes': order.notes
            })

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"Error getting orders: {str(e)}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/orders/<int:order_id>/status', methods=['PUT'])
@login_required
def update_order_status(order_id):
    """Update order status"""
    order = Order.query.get_or_404(order_id)
    data = request.get_json()

    new_status = data.get('status')
    if not new_status:
        return jsonify({'error': 'Status is required'}), 400

    valid_statuses = ['pending', 'confirmed', 'shipped', 'delivered', 'cancelled']
    if new_status not in valid_statuses:
        return jsonify({'error': f'Invalid status. Must be one of: {", ".join(valid_statuses)}'}), 400

    try:
        order.status = new_status
        if 'notes' in data:
            order.notes = data['notes']

        db.session.commit()

        return jsonify({
            'message': 'Order status updated successfully',
            'order_id': order.id,
            'status': order.status
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating order status: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ----------------------
# Inventory Management
# ----------------------

@api_bp.route('/inventory', methods=['POST'])
@login_required
def create_inventory():
    """Create inventory entry from order"""
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400

    order_id = data.get('order_id')
    if not order_id:
        return jsonify({"error": "order_id is required"}), 400

    try:
        # Get the order
        order = Order.query.get(order_id)
        if not order:
            return jsonify({"error": "Order not found"}), 404

        # Create inventory entry
        inventory = Inventory(
            order_id=order.id,
            customer_id=order.customer_id,
            product_name=order.product_name,
            quantity=order.quantity,
            supplier_name=order.supplier_name,
            arrival_date=data.get('arrival_date'),
            shipping_method=data.get('shipping_method'),
            shipping_date=data.get('shipping_date')
        )

        db.session.add(inventory)
        db.session.flush()  # Get inventory ID

        # Add serial numbers if provided
        serial_numbers = data.get('serial_numbers', [])
        for serial_num in serial_numbers:
            inventory_serial = InventorySerialNumber(
                inventory_id=inventory.id,
                serial_number=serial_num,
                status='in_stock'
            )
            db.session.add(inventory_serial)

        db.session.commit()

        return jsonify({
            "message": "Inventory created successfully",
            "inventory_id": inventory.id
        }), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating inventory: {str(e)}")
        return jsonify({"error": f"Failed to create inventory: {str(e)}"}), 500

@api_bp.route('/inventory', methods=['GET'])
@login_required
def get_inventory():
    """Get all inventory entries"""
    try:
        customer_id = request.args.get('customer_id')
        product_name = request.args.get('product_name')
        status = request.args.get('status')

        query = Inventory.query.join(Customer)

        if customer_id:
            query = query.filter(Inventory.customer_id == customer_id)

        if product_name:
            query = query.filter(Inventory.product_name.ilike(f'%{product_name}%'))

        inventory_items = query.order_by(Inventory.created_at.desc()).all()

        result = []
        for item in inventory_items:
            serial_numbers = []
            for serial in item.serial_numbers:
                if not status or serial.status == status:
                    serial_numbers.append({
                        'id': serial.id,
                        'serial_number': serial.serial_number,
                        'status': serial.status,
                        'created_at': serial.created_at.isoformat(),
                        'shipped_at': serial.shipped_at.isoformat() if serial.shipped_at else None
                    })

            if not status or serial_numbers:  # Include if no status filter or has matching serials
                result.append({
                    'id': item.id,
                    'order_id': item.order_id,
                    'customer_id': item.customer_id,
                    'customer_name': item.customer.name,
                    'product_name': item.product_name,
                    'quantity': item.quantity,
                    'supplier_name': item.supplier_name,
                    'arrival_date': item.arrival_date.isoformat() if item.arrival_date else None,
                    'shipping_method': item.shipping_method,
                    'shipping_date': item.shipping_date.isoformat() if item.shipping_date else None,
                    'created_at': item.created_at.isoformat(),
                    'serial_numbers': serial_numbers
                })

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"Error getting inventory: {str(e)}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/inventory/<int:inventory_id>/ship', methods=['POST'])
@login_required
def ship_inventory_items():
    """Ship inventory items by updating serial number status"""
    inventory = Inventory.query.get_or_404(inventory_id)
    data = request.get_json()

    serial_numbers = data.get('serial_numbers', [])
    if not serial_numbers:
        return jsonify({'error': 'Serial numbers are required'}), 400

    try:
        shipped_count = 0
        for serial_num in serial_numbers:
            serial = InventorySerialNumber.query.filter_by(
                inventory_id=inventory_id,
                serial_number=serial_num,
                status='in_stock'
            ).first()

            if serial:
                serial.status = 'shipped'
                serial.shipped_at = datetime.now()
                shipped_count += 1

        db.session.commit()

        return jsonify({
            'message': f'Successfully shipped {shipped_count} items',
            'shipped_count': shipped_count
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error shipping inventory: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ----------------------
# User Management
# ----------------------

@api_bp.route('/users', methods=['GET'])
@login_required
def get_users():
    """Get all users (admin only)"""
    if not current_user.is_admin:
        return jsonify({'error': 'Admin access required'}), 403

    try:
        users = User.query.order_by(User.username).all()
        return jsonify([user.to_dict() for user in users])
    except Exception as e:
        current_app.logger.error(f"Error getting users: {str(e)}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/users', methods=['POST'])
@login_required
def create_user():
    """Create a new user (admin only)"""
    if not current_user.is_admin:
        return jsonify({'error': 'Admin access required'}), 403

    data = request.get_json()

    required_fields = ['username', 'email', 'password']
    for field in required_fields:
        if not data.get(field):
            return jsonify({'error': f'{field} is required'}), 400

    try:
        # Check if user already exists
        if User.query.filter_by(username=data['username']).first():
            return jsonify({'error': 'Username already exists'}), 400

        if User.query.filter_by(email=data['email']).first():
            return jsonify({'error': 'Email already exists'}), 400

        user = User(
            username=data['username'],
            email=data['email'],
            is_admin=data.get('is_admin', False),
            first_name=data.get('first_name'),
            last_name=data.get('last_name'),
            phone=data.get('phone')
        )
        user.set_password(data['password'])

        db.session.add(user)
        db.session.commit()

        return jsonify({
            'message': 'User created successfully',
            'user': user.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating user: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ----------------------
# Audit and Reporting
# ----------------------

@api_bp.route('/audit', methods=['GET'])
@login_required
def get_audit_logs():
    """Get audit logs (admin only)"""
    if not current_user.is_admin:
        return jsonify({'error': 'Admin access required'}), 403

    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 50))
        user_id = request.args.get('user_id')
        action_type = request.args.get('action_type')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        query = UserActivityLog.query.join(User)

        if user_id:
            query = query.filter(UserActivityLog.user_id == user_id)

        if action_type:
            query = query.filter(UserActivityLog.action_type.ilike(f'%{action_type}%'))

        if start_date:
            query = query.filter(UserActivityLog.created_at >= start_date)

        if end_date:
            query = query.filter(UserActivityLog.created_at <= end_date)

        logs = query.order_by(UserActivityLog.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        result = []
        for log in logs.items:
            result.append({
                'id': log.id,
                'user_id': log.user_id,
                'username': log.user.username,
                'action_type': log.action_type,
                'description': log.description,
                'details': log.details,
                'ip_address': log.ip_address,
                'user_agent': log.user_agent,
                'endpoint': log.endpoint,
                'method': log.method,
                'status_code': log.status_code,
                'created_at': log.created_at.isoformat()
            })

        return jsonify({
            'logs': result,
            'pagination': {
                'page': logs.page,
                'pages': logs.pages,
                'per_page': logs.per_page,
                'total': logs.total,
                'has_next': logs.has_next,
                'has_prev': logs.has_prev
            }
        })

    except Exception as e:
        current_app.logger.error(f"Error getting audit logs: {str(e)}")
        return jsonify({'error': str(e)}), 500

# ----------------------
# Excel Export Routes
# ----------------------

@api_bp.route('/export/inquiries')
@login_required
def export_inquiries():
    """Export inquiries to Excel"""
    try:
        import openpyxl
        from openpyxl.styles import Font, Alignment
        from io import BytesIO
        from flask import send_file

        # Get filter parameters
        customer_id = request.args.get('customer_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        # Build query
        query = ProductInquiry.query.join(Customer)

        if customer_id:
            query = query.filter(ProductInquiry.customer_id == customer_id)
        if start_date:
            query = query.filter(ProductInquiry.inquiry_date >= start_date)
        if end_date:
            query = query.filter(ProductInquiry.inquiry_date <= end_date)

        inquiries = query.order_by(ProductInquiry.inquiry_date.desc()).all()

        # Create workbook
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Product Inquiries"

        # Headers
        headers = [
            'Inquiry Date', 'Customer Name', 'Product Name', 'Brand',
            'Quantity', 'Expected Delivery Days', 'My Price'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # Data rows
        for row, inquiry in enumerate(inquiries, 2):
            ws.cell(row=row, column=1, value=inquiry.inquiry_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=2, value=inquiry.customer.name)
            ws.cell(row=row, column=3, value=inquiry.product_name)
            ws.cell(row=row, column=4, value=inquiry.brand or '')
            ws.cell(row=row, column=5, value=inquiry.quantity)
            ws.cell(row=row, column=6, value=inquiry.expected_delivery_days)
            ws.cell(row=row, column=7, value=inquiry.my_price)

        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # Save to BytesIO
        output = BytesIO()
        wb.save(output)
        output.seek(0)

        filename = f"inquiries_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        current_app.logger.error(f"Error exporting inquiries: {str(e)}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/export/orders')
@login_required
def export_orders():
    """Export orders to Excel"""
    try:
        import openpyxl
        from openpyxl.styles import Font, Alignment
        from io import BytesIO
        from flask import send_file

        # Get filter parameters
        customer_id = request.args.get('customer_id')
        status = request.args.get('status')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        # Build query
        query = Order.query.join(Customer)

        if customer_id:
            query = query.filter(Order.customer_id == customer_id)
        if status:
            query = query.filter(Order.status == status)
        if start_date:
            query = query.filter(Order.order_date >= start_date)
        if end_date:
            query = query.filter(Order.order_date <= end_date)

        orders = query.order_by(Order.order_date.desc()).all()

        # Create workbook
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Orders"

        # Headers
        headers = [
            'Order Date', 'Customer Name', 'Product Name', 'Brand',
            'Quantity', 'Price', 'My Price', 'Supplier Name',
            'Delivery Days', 'Expected Delivery', 'Status'
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # Data rows
        for row, order in enumerate(orders, 2):
            ws.cell(row=row, column=1, value=order.order_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=2, value=order.customer.name)
            ws.cell(row=row, column=3, value=order.product_name)
            ws.cell(row=row, column=4, value=order.brand or '')
            ws.cell(row=row, column=5, value=order.quantity)
            ws.cell(row=row, column=6, value=order.price)
            ws.cell(row=row, column=7, value=order.my_price)
            ws.cell(row=row, column=8, value=order.supplier_name)
            ws.cell(row=row, column=9, value=order.delivery_days)
            ws.cell(row=row, column=10, value=order.expected_delivery_date.strftime('%Y-%m-%d') if order.expected_delivery_date else '')
            ws.cell(row=row, column=11, value=order.status)

        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # Save to BytesIO
        output = BytesIO()
        wb.save(output)
        output.seek(0)

        filename = f"orders_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        current_app.logger.error(f"Error exporting orders: {str(e)}")
        return jsonify({'error': str(e)}), 500
