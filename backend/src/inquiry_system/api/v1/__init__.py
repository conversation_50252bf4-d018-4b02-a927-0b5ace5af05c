"""
API v1 module for the Inquiry System.

This module contains version 1 of the REST API.
"""

from flask import Blueprint

# Create the main API v1 blueprint
api_v1_bp = Blueprint('api_v1', __name__)

# Import and register sub-blueprints
from .auth import auth_bp
from .customers import customers_bp
from .inquiries import inquiries_bp
from .suppliers import suppliers_bp
from .orders import orders_bp
from .inventory import inventory_bp
from .users import users_bp
from .health import health_bp

# Register sub-blueprints
api_v1_bp.register_blueprint(auth_bp, url_prefix='/auth')
api_v1_bp.register_blueprint(customers_bp, url_prefix='/customers')
api_v1_bp.register_blueprint(inquiries_bp, url_prefix='/inquiries')
api_v1_bp.register_blueprint(suppliers_bp, url_prefix='/suppliers')
api_v1_bp.register_blueprint(orders_bp, url_prefix='/orders')
api_v1_bp.register_blueprint(inventory_bp, url_prefix='/inventory')
api_v1_bp.register_blueprint(users_bp, url_prefix='/users')
api_v1_bp.register_blueprint(health_bp, url_prefix='/health')

__all__ = ['api_v1_bp']
