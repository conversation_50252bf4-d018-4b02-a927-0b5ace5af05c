"""
Authentication endpoints for API v1.

This module provides authentication and authorization endpoints.
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.exceptions import BadRequest

from inquiry_system.models.user import User, UserActivityLog
from inquiry_system.core.database import db
from inquiry_system.utils.exceptions import ValidationError, AuthenticationError
from inquiry_system.utils.validators import validate_email, validate_string_length

auth_bp = Blueprint('auth', __name__)


@auth_bp.route('/login', methods=['POST'])
def login():
    """
    User login endpoint.
    
    Expected JSON payload:
    {
        "username": "<EMAIL>",
        "password": "password123"
    }
    """
    try:
        data = request.get_json()
        if not data:
            raise BadRequest("No JSON data provided")
        
        username = data.get('username', '').strip()
        password = data.get('password', '')
        
        # Validate input
        if not username:
            raise ValidationError("Username or email is required", field="username")
        if not password:
            raise ValidationError("Password is required", field="password")
        
        # Find user by username or email
        user = User.query.filter(
            (User.username == username) | (User.email == username)
        ).first()
        
        if not user or not user.check_password(password):
            # Log failed login attempt
            UserActivityLog.log_activity(
                user_id=user.id if user else None,
                action_type="login_failed",
                description="Failed login attempt",
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent'),
                endpoint=request.endpoint,
                method=request.method
            )
            raise AuthenticationError("Invalid username or password")
        
        if not user.is_active:
            raise AuthenticationError("Account is disabled")
        
        # Log successful login
        login_user(user)
        user.update_last_login()
        
        UserActivityLog.log_activity(
            user_id=user.id,
            action_type="login_success",
            description="Successful login",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent'),
            endpoint=request.endpoint,
            method=request.method,
            status_code=200
        )
        
        return jsonify({
            'message': 'Login successful',
            'user': user.to_dict()
        }), 200
        
    except (ValidationError, AuthenticationError) as e:
        return jsonify({'error': str(e)}), e.status_code
    except Exception as e:
        current_app.logger.error(f'Login error: {e}')
        return jsonify({'error': 'Internal server error'}), 500


@auth_bp.route('/logout', methods=['POST'])
@login_required
def logout():
    """User logout endpoint."""
    try:
        # Log logout activity
        UserActivityLog.log_activity(
            user_id=current_user.id,
            action_type="logout",
            description="User logout",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent'),
            endpoint=request.endpoint,
            method=request.method,
            status_code=200
        )
        
        logout_user()
        
        return jsonify({'message': 'Logout successful'}), 200
        
    except Exception as e:
        current_app.logger.error(f'Logout error: {e}')
        return jsonify({'error': 'Internal server error'}), 500


@auth_bp.route('/me', methods=['GET'])
@login_required
def get_current_user():
    """Get current user information."""
    return jsonify({
        'user': current_user.to_dict()
    }), 200


@auth_bp.route('/check', methods=['GET'])
def check_authentication():
    """Check if user is authenticated."""
    if current_user.is_authenticated:
        return jsonify({
            'authenticated': True,
            'user': current_user.to_dict()
        }), 200
    else:
        return jsonify({
            'authenticated': False
        }), 200
