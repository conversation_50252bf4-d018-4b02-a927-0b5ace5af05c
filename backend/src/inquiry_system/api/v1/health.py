"""
Health check endpoints for API v1.

This module provides health check and status endpoints.
"""

from datetime import datetime
from flask import Blueprint, jsonify, current_app
from sqlalchemy import text

from inquiry_system.core.database import db

health_bp = Blueprint('health', __name__)


@health_bp.route('/', methods=['GET'])
def health_check():
    """
    Basic health check endpoint.
    
    Returns:
        JSON response with health status
    """
    try:
        # Test database connection
        db.session.execute(text('SELECT 1'))
        db_status = 'healthy'
    except Exception as e:
        current_app.logger.error(f'Database health check failed: {e}')
        db_status = 'unhealthy'
    
    status = {
        'status': 'healthy' if db_status == 'healthy' else 'unhealthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': current_app.config.get('APP_VERSION', '2.0.0'),
        'environment': current_app.config.get('FLASK_ENV', 'unknown'),
        'database': db_status
    }
    
    status_code = 200 if status['status'] == 'healthy' else 503
    return jsonify(status), status_code


@health_bp.route('/detailed', methods=['GET'])
def detailed_health_check():
    """
    Detailed health check endpoint with component status.
    
    Returns:
        JSON response with detailed health information
    """
    components = {}
    overall_status = 'healthy'
    
    # Database check
    try:
        db.session.execute(text('SELECT 1'))
        components['database'] = {
            'status': 'healthy',
            'message': 'Database connection successful'
        }
    except Exception as e:
        components['database'] = {
            'status': 'unhealthy',
            'message': f'Database connection failed: {str(e)}'
        }
        overall_status = 'unhealthy'
    
    # Cache check (if configured)
    try:
        from inquiry_system.core.extensions import cache
        cache.set('health_check', 'test', timeout=1)
        test_value = cache.get('health_check')
        if test_value == 'test':
            components['cache'] = {
                'status': 'healthy',
                'message': 'Cache is working'
            }
        else:
            components['cache'] = {
                'status': 'degraded',
                'message': 'Cache test failed'
            }
    except Exception as e:
        components['cache'] = {
            'status': 'unavailable',
            'message': f'Cache not available: {str(e)}'
        }
    
    # Application info
    app_info = {
        'name': current_app.config.get('APP_NAME', 'Inquiry System'),
        'version': current_app.config.get('APP_VERSION', '2.0.0'),
        'environment': current_app.config.get('FLASK_ENV', 'unknown'),
        'debug': current_app.debug,
        'testing': current_app.testing
    }
    
    response = {
        'status': overall_status,
        'timestamp': datetime.utcnow().isoformat(),
        'application': app_info,
        'components': components
    }
    
    status_code = 200 if overall_status == 'healthy' else 503
    return jsonify(response), status_code


@health_bp.route('/ready', methods=['GET'])
def readiness_check():
    """
    Readiness check endpoint for Kubernetes/Docker.
    
    Returns:
        JSON response indicating if the service is ready to serve traffic
    """
    try:
        # Check if database is accessible
        db.session.execute(text('SELECT 1'))
        
        # Check if required tables exist
        from inquiry_system.models.user import User
        User.query.first()  # This will fail if tables don't exist
        
        return jsonify({
            'status': 'ready',
            'timestamp': datetime.utcnow().isoformat()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'Readiness check failed: {e}')
        return jsonify({
            'status': 'not_ready',
            'timestamp': datetime.utcnow().isoformat(),
            'error': str(e)
        }), 503


@health_bp.route('/live', methods=['GET'])
def liveness_check():
    """
    Liveness check endpoint for Kubernetes/Docker.
    
    Returns:
        JSON response indicating if the service is alive
    """
    return jsonify({
        'status': 'alive',
        'timestamp': datetime.utcnow().isoformat()
    }), 200
