"""
Inquiry models for the Inquiry System.

This module contains product inquiry-related database models.
"""

from inquiry_system.core.database import db


class ProductInquiry(db.Model):
    """Product inquiry model for managing customer product requests."""

    __tablename__ = 'product_inquiry'

    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    brand = db.Column(db.String(100))
    product_name = db.Column(db.String(100), nullable=False, index=True)
    quantity = db.Column(db.Integer, nullable=False)
    expected_delivery_days = db.Column(db.Integer, nullable=False)
    my_price = db.Column(db.Float, nullable=False)
    inquiry_date = db.Column(db.DateTime, nullable=False, default=db.func.now())

    # Relationships
    supplier_quotes = db.relationship('SupplierQuote', back_populates='inquiry', lazy=True)
    orders = db.relationship('Order', backref='inquiry', lazy=True)
    serial_numbers = db.relationship('SerialNumber', backref='inquiry', lazy=True)

    def __repr__(self):
        return f"<ProductInquiry(product={self.product_name})>"


class SerialNumber(db.Model):
    """Serial number model for tracking product serial numbers."""

    __tablename__ = 'serial_number'

    id = db.Column(db.Integer, primary_key=True)
    inquiry_id = db.Column(db.Integer, db.ForeignKey('product_inquiry.id'), nullable=False)
    serial_number = db.Column(db.String(200), nullable=False, index=True)
    created_at = db.Column(db.DateTime, default=db.func.now())

    def __repr__(self):
        return f"<SerialNumber(serial={self.serial_number})>"
