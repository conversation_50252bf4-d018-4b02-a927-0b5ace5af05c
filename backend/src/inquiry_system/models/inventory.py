"""
Inventory models for the Inquiry System.

This module contains inventory-related database models.
"""

from datetime import datetime
from inquiry_system.core.database import db


class Inventory(db.Model):
    """Inventory model for managing stock."""

    __tablename__ = 'inventory'

    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.Foreign<PERSON>ey('order.id'), nullable=False)
    customer_id = db.Column(db.Integer, db.<PERSON>ey('customer.id'), nullable=False)
    product_name = db.Column(db.String(200), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    arrival_date = db.Column(db.DateTime)
    shipping_method = db.Column(db.String(50))  # 'sea' or 'air'
    shipping_date = db.Column(db.DateTime)
    supplier_name = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    order = db.relationship('Order', backref=db.backref('inventory', lazy=True))
    customer = db.relationship('Customer', backref=db.backref('inventory', lazy=True))
    serial_numbers = db.relationship('InventorySerialNumber', backref='inventory', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f"<Inventory(product={self.product_name}, quantity={self.quantity})>"


class InventorySerialNumber(db.Model):
    """Inventory serial number model for tracking individual items."""

    __tablename__ = 'inventory_serial_number'

    id = db.Column(db.Integer, primary_key=True)
    inventory_id = db.Column(db.Integer, db.ForeignKey('inventory.id'), nullable=False)
    serial_number = db.Column(db.String(200), nullable=False, index=True)
    status = db.Column(db.String(20), default='in_stock')  # in_stock, shipped
    created_at = db.Column(db.DateTime, default=datetime.now)
    shipped_at = db.Column(db.DateTime)

    def __repr__(self):
        return f"<InventorySerialNumber(serial={self.serial_number}, status={self.status})>"
