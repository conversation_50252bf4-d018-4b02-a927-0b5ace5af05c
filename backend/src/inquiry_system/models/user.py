"""
User models for the Inquiry System.

This module contains user-related database models.
"""

from datetime import datetime
from typing import Optional

from flask_login import UserMixin
from werkzeug.security import check_password_hash, generate_password_hash

from inquiry_system.core.database import db


class User(db.Model, UserMixin):
    """User model for authentication and authorization."""

    __tablename__ = 'user'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    is_admin = db.Column(db.Bo<PERSON>, default=False, nullable=False)
    first_name = db.Column(db.String(50))
    last_name = db.Column(db.String(50))
    phone = db.Column(db.String(20))
    last_login = db.Column(db.DateTime)
    password_changed_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    activity_logs = db.relationship('UserActivityLog', backref='user', lazy='dynamic')
    
    def set_password(self, password: str) -> None:
        """Set user password with hashing."""
        self.password_hash = generate_password_hash(password, method='pbkdf2:sha256')
        self.password_changed_at = datetime.utcnow()
    
    def check_password(self, password: str) -> bool:
        """Check if provided password matches the stored hash."""
        return check_password_hash(self.password_hash, password)
    
    def update_last_login(self) -> None:
        """Update the last login timestamp."""
        self.last_login = datetime.utcnow()
        db.session.commit()
    
    @property
    def full_name(self) -> str:
        """Get user's full name."""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        elif self.last_name:
            return self.last_name
        else:
            return self.username
    
    @property
    def is_authenticated(self) -> bool:
        """Check if user is authenticated (required by Flask-Login)."""
        return True
    
    @property
    def is_anonymous(self) -> bool:
        """Check if user is anonymous (required by Flask-Login)."""
        return False
    
    def get_id(self) -> str:
        """Get user ID as string (required by Flask-Login)."""
        return str(self.id)
    
    def to_dict(self, include_sensitive: bool = False) -> dict:
        """Convert user to dictionary, optionally including sensitive data."""
        data = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'is_active': self.is_active,
            'is_admin': self.is_admin,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'phone': self.phone,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'full_name': self.full_name
        }

        # Include sensitive information when requested
        if include_sensitive:
            data['password_hash'] = self.password_hash

        return data
    
    def __repr__(self) -> str:
        return f"<User(username='{self.username}', email='{self.email}')>"


class UserActivityLog(db.Model):
    """Model for logging user activities."""

    __tablename__ = 'user_activity_log'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, index=True)
    action_type = db.Column(db.String(50), nullable=False, index=True)
    description = db.Column(db.String(255))
    details = db.Column(db.Text)  # JSON string for additional details
    ip_address = db.Column(db.String(45))  # IPv6 addresses can be up to 45 characters
    user_agent = db.Column(db.String(255))
    endpoint = db.Column(db.String(255))
    method = db.Column(db.String(10))
    status_code = db.Column(db.Integer)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    @classmethod
    def log_activity(
        cls,
        user_id: int,
        action_type: str,
        description: Optional[str] = None,
        details: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        endpoint: Optional[str] = None,
        method: Optional[str] = None,
        status_code: Optional[int] = None
    ) -> 'UserActivityLog':
        """Create and save an activity log entry."""
        log_entry = cls(
            user_id=user_id,
            action_type=action_type,
            description=description,
            details=details,
            ip_address=ip_address,
            user_agent=user_agent,
            endpoint=endpoint,
            method=method,
            status_code=status_code
        )
        db.session.add(log_entry)
        db.session.commit()
        return log_entry
    
    def __repr__(self) -> str:
        return f"<UserActivityLog(user_id={self.user_id}, action='{self.action_type}')>"
