"""
Supplier models for the Inquiry System.

This module contains supplier-related database models.
"""

from inquiry_system.core.database import db


class Supplier(db.Model):
    """Supplier model for managing supplier information."""

    __tablename__ = 'supplier'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, index=True)
    contact = db.Column(db.String(100))
    default_delivery_days = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=db.func.now())
    updated_at = db.Column(db.DateTime, default=db.func.now(), onupdate=db.func.now())

    # Relationships
    quotes = db.relationship('SupplierQuote', back_populates='supplier', lazy=True)

    def __repr__(self):
        return f"<Supplier(name={self.name})>"


class SupplierQuote(db.Model):
    """Supplier quote model for managing price quotes."""

    __tablename__ = 'supplier_quote'

    id = db.Column(db.Integer, primary_key=True)
    inquiry_id = db.Column(db.Integer, db.ForeignKey('product_inquiry.id'), nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('supplier.id'), nullable=False)
    price = db.Column(db.Float, nullable=False)
    my_price = db.Column(db.Float, nullable=False)
    delivery_days = db.Column(db.Integer, nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    product_name = db.Column(db.String(100), nullable=False)
    brand = db.Column(db.String(100))
    inquiry_date = db.Column(db.DateTime, nullable=False, default=db.func.now())

    # Relationships
    inquiry = db.relationship('ProductInquiry', back_populates='supplier_quotes', lazy=True)
    supplier = db.relationship('Supplier', back_populates='quotes', lazy=True)
    orders = db.relationship('Order', backref='supplier_quote', lazy=True)

    def __init__(self, **kwargs):
        inquiry = kwargs.get('inquiry')
        if inquiry:
            kwargs.setdefault('product_name', inquiry.product_name)
            kwargs.setdefault('brand', inquiry.brand)
            kwargs.setdefault('quantity', inquiry.quantity)
            kwargs.setdefault('my_price', inquiry.my_price)
        super(SupplierQuote, self).__init__(**kwargs)

    def __repr__(self):
        return f"<SupplierQuote(supplier={self.supplier.name}, product={self.product_name}, price={self.price})>"
