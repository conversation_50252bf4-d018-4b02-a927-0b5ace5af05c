"""
Order models for the Inquiry System.

This module contains order-related database models.
"""

from datetime import datetime, timedelta
from inquiry_system.core.database import db


class Order(db.Model):
    """Order model for managing customer orders."""

    __tablename__ = 'order'

    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    inquiry_id = db.Column(db.Integer, db.ForeignKey('product_inquiry.id'), nullable=False)
    supplier_quote_id = db.Column(db.Integer, db.ForeignKey('supplier_quote.id'), nullable=False)
    order_date = db.Column(db.DateTime, nullable=False, default=datetime.now)
    status = db.Column(db.String(20), nullable=False, default='pending', index=True)
    notes = db.Column(db.Text)

    product_name = db.Column(db.String(100), nullable=False)
    brand = db.Column(db.String(100))
    quantity = db.Column(db.Integer, nullable=False)
    price = db.Column(db.Float, nullable=False)
    my_price = db.Column(db.Float, nullable=False)
    supplier_name = db.Column(db.String(100), nullable=False)
    delivery_days = db.Column(db.Integer, nullable=False)
    expected_delivery_date = db.Column(db.DateTime)

    def __init__(self, **kwargs):
        supplier_quote = kwargs.get('supplier_quote')
        if supplier_quote:
            # Copy fields from supplier_quote
            kwargs.setdefault('product_name', supplier_quote.product_name)
            kwargs.setdefault('brand', supplier_quote.brand)
            kwargs.setdefault('quantity', supplier_quote.quantity)
            kwargs.setdefault('price', supplier_quote.price)
            kwargs.setdefault('my_price', supplier_quote.my_price)
            kwargs.setdefault('supplier_name', supplier_quote.supplier.name)
            kwargs.setdefault('delivery_days', supplier_quote.delivery_days)
            # Copy customer_id from inquiry
            kwargs.setdefault('customer_id', supplier_quote.inquiry.customer_id)

        super(Order, self).__init__(**kwargs)

        # Set expected_delivery_date based on order_date and delivery_days using local time
        if self.delivery_days is not None and self.order_date is not None:
            self.expected_delivery_date = self.order_date + timedelta(days=self.delivery_days)

    def __repr__(self):
        return f"<Order(product={self.product_name}, customer_id={self.customer_id})>"
