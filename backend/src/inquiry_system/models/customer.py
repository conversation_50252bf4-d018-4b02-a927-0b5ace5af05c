"""
Customer models for the Inquiry System.

This module contains customer-related database models.
"""

from inquiry_system.core.database import db


class Customer(db.Model):
    """Customer model for managing customer information."""
    
    __tablename__ = 'customer'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, index=True)
    address = db.Column(db.String(200), nullable=False)  # 客户地址
    phone = db.Column(db.String(20), nullable=False)     # 客户电话
    contact = db.Column(db.String(100), nullable=False)  # 联系人
    country = db.Column(db.String(50), nullable=False)   # 客户国家
    inquiries = db.relationship('ProductInquiry', backref='customer', lazy=True)
    orders = db.relationship('Order', backref='customer', lazy=True)

    def __repr__(self):
        return f"<Customer(name={self.name})>"
