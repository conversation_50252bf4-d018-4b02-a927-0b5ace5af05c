"""
Base model for the Inquiry System.

This module contains the base model class with common functionality.
"""

from datetime import datetime
from typing import Any, Dict

from sqlalchemy import Column, DateTime, Integer
from sqlalchemy.ext.declarative import declared_attr


# Import db from core to avoid circular imports
def get_db():
    from inquiry_system.core.database import db
    return db

class BaseModel:
    """Base model class with common fields and methods."""
    
    __abstract__ = True
    
    # Primary key
    id = Column(Integer, primary_key=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow, 
        nullable=False
    )
    
    @declared_attr
    def __tablename__(cls) -> str:
        """Generate table name from class name."""
        # Convert CamelCase to snake_case
        import re
        name = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', cls.__name__)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', name).lower()
    
    def to_dict(self, include_relationships: bool = False) -> Dict[str, Any]:
        """Convert model instance to dictionary."""
        result = {}
        
        # Include column attributes
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                value = value.isoformat()
            result[column.name] = value
        
        # Include relationships if requested
        if include_relationships:
            for relationship in self.__mapper__.relationships:
                value = getattr(self, relationship.key)
                if value is not None:
                    if hasattr(value, '__iter__') and not isinstance(value, str):
                        # Collection relationship
                        result[relationship.key] = [
                            item.to_dict() if hasattr(item, 'to_dict') else str(item)
                            for item in value
                        ]
                    else:
                        # Single relationship
                        result[relationship.key] = (
                            value.to_dict() if hasattr(value, 'to_dict') else str(value)
                        )
        
        return result
    
    def update(self, **kwargs) -> None:
        """Update model instance with provided keyword arguments."""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.updated_at = datetime.utcnow()
    
    def save(self) -> 'BaseModel':
        """Save the model instance to the database."""
        db = get_db()
        db.session.add(self)
        db.session.commit()
        return self

    def delete(self) -> None:
        """Delete the model instance from the database."""
        db = get_db()
        db.session.delete(self)
        db.session.commit()
    
    @classmethod
    def create(cls, **kwargs) -> 'BaseModel':
        """Create and save a new model instance."""
        instance = cls(**kwargs)
        return instance.save()
    
    @classmethod
    def get_by_id(cls, id: int) -> 'BaseModel':
        """Get model instance by ID."""
        return cls.query.get(id)
    
    @classmethod
    def get_or_404(cls, id: int) -> 'BaseModel':
        """Get model instance by ID or raise 404 error."""
        return cls.query.get_or_404(id)
    
    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<{self.__class__.__name__}(id={self.id})>"
