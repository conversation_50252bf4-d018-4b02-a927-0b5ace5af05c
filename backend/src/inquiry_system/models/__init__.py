"""
Models module for the Inquiry System.

This module contains all database models and related utilities.
"""

from .customer import Customer
from .inquiry import ProductInquiry, SerialNumber
from .supplier import Supplier, SupplierQuote
from .order import Order
from .inventory import Inventory, InventorySerialNumber
from .user import User, UserActivityLog

__all__ = [
    "Customer",
    "ProductInquiry",
    "SerialNumber",
    "Supplier",
    "SupplierQuote",
    "Order",
    "Inventory",
    "InventorySerialNumber",
    "User",
    "UserActivityLog",
]
