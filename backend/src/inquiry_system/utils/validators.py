"""
Validation utilities for the Inquiry System.

This module contains validation functions for common data types.
"""

import re
from datetime import datetime, date
from typing import Op<PERSON>, Tuple, Union
from email_validator import validate_email as _validate_email, EmailNotValidError

from inquiry_system.utils.exceptions import ValidationError


def validate_email(email: str) -> str:
    """
    Validate email address format.
    
    Args:
        email: Email address to validate
        
    Returns:
        Normalized email address
        
    Raises:
        ValidationError: If email format is invalid
    """
    try:
        # Use email-validator library for robust validation
        valid = _validate_email(email)
        return valid.email
    except EmailNotValidError as e:
        raise ValidationError(f"Invalid email address: {str(e)}", field="email")


def validate_phone(phone: str) -> str:
    """
    Validate phone number format.
    
    Args:
        phone: Phone number to validate
        
    Returns:
        Normalized phone number
        
    Raises:
        ValidationError: If phone format is invalid
    """
    if not phone:
        raise ValidationError("Phone number is required", field="phone")
    
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone)
    
    # Check if it's a valid length (7-15 digits)
    if len(digits_only) < 7 or len(digits_only) > 15:
        raise ValidationError(
            "Phone number must be between 7 and 15 digits", 
            field="phone"
        )
    
    return digits_only


def validate_date_range(
    start_date: Union[str, date, datetime], 
    end_date: Union[str, date, datetime]
) -> Tuple[date, date]:
    """
    Validate date range.
    
    Args:
        start_date: Start date
        end_date: End date
        
    Returns:
        Tuple of (start_date, end_date) as date objects
        
    Raises:
        ValidationError: If date range is invalid
    """
    # Convert strings to date objects
    if isinstance(start_date, str):
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        except ValueError:
            raise ValidationError(
                "Invalid start date format. Use YYYY-MM-DD", 
                field="start_date"
            )
    elif isinstance(start_date, datetime):
        start_date = start_date.date()
    
    if isinstance(end_date, str):
        try:
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        except ValueError:
            raise ValidationError(
                "Invalid end date format. Use YYYY-MM-DD", 
                field="end_date"
            )
    elif isinstance(end_date, datetime):
        end_date = end_date.date()
    
    # Validate range
    if start_date > end_date:
        raise ValidationError(
            "Start date must be before or equal to end date",
            field="date_range"
        )
    
    return start_date, end_date


def validate_positive_number(value: Union[int, float, str], field_name: str) -> Union[int, float]:
    """
    Validate that a value is a positive number.
    
    Args:
        value: Value to validate
        field_name: Name of the field for error messages
        
    Returns:
        Validated number
        
    Raises:
        ValidationError: If value is not a positive number
    """
    try:
        if isinstance(value, str):
            # Try to convert to float first, then int if it's a whole number
            num_value = float(value)
            if num_value.is_integer():
                num_value = int(num_value)
        else:
            num_value = value
        
        if num_value <= 0:
            raise ValidationError(
                f"{field_name} must be a positive number",
                field=field_name.lower().replace(' ', '_')
            )
        
        return num_value
    except (ValueError, TypeError):
        raise ValidationError(
            f"{field_name} must be a valid number",
            field=field_name.lower().replace(' ', '_')
        )


def validate_string_length(
    value: str, 
    field_name: str, 
    min_length: int = 1, 
    max_length: Optional[int] = None
) -> str:
    """
    Validate string length.
    
    Args:
        value: String to validate
        field_name: Name of the field for error messages
        min_length: Minimum allowed length
        max_length: Maximum allowed length
        
    Returns:
        Validated string
        
    Raises:
        ValidationError: If string length is invalid
    """
    if not isinstance(value, str):
        raise ValidationError(
            f"{field_name} must be a string",
            field=field_name.lower().replace(' ', '_')
        )
    
    value = value.strip()
    
    if len(value) < min_length:
        raise ValidationError(
            f"{field_name} must be at least {min_length} characters long",
            field=field_name.lower().replace(' ', '_')
        )
    
    if max_length and len(value) > max_length:
        raise ValidationError(
            f"{field_name} must be no more than {max_length} characters long",
            field=field_name.lower().replace(' ', '_')
        )
    
    return value


def validate_choice(value: str, choices: list, field_name: str) -> str:
    """
    Validate that a value is in a list of allowed choices.
    
    Args:
        value: Value to validate
        choices: List of allowed choices
        field_name: Name of the field for error messages
        
    Returns:
        Validated value
        
    Raises:
        ValidationError: If value is not in choices
    """
    if value not in choices:
        raise ValidationError(
            f"{field_name} must be one of: {', '.join(choices)}",
            field=field_name.lower().replace(' ', '_')
        )
    
    return value
