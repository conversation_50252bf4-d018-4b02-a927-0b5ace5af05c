"""
Custom exceptions for the Inquiry System.

This module defines custom exception classes for better error handling.
"""


class InquirySystemError(Exception):
    """Base exception class for Inquiry System errors."""
    
    def __init__(self, message: str, status_code: int = 400):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
    
    def __str__(self) -> str:
        return self.message


class ValidationError(InquirySystemError):
    """Exception raised for validation errors."""
    
    def __init__(self, message: str, field: str = None):
        super().__init__(message, status_code=400)
        self.field = field


class NotFoundError(InquirySystemError):
    """Exception raised when a resource is not found."""
    
    def __init__(self, message: str = "Resource not found"):
        super().__init__(message, status_code=404)


class AuthenticationError(InquirySystemError):
    """Exception raised for authentication errors."""
    
    def __init__(self, message: str = "Authentication required"):
        super().__init__(message, status_code=401)


class AuthorizationError(InquirySystemError):
    """Exception raised for authorization errors."""
    
    def __init__(self, message: str = "Insufficient permissions"):
        super().__init__(message, status_code=403)


class ConflictError(InquirySystemError):
    """Exception raised for resource conflicts."""
    
    def __init__(self, message: str = "Resource conflict"):
        super().__init__(message, status_code=409)


class RateLimitError(InquirySystemError):
    """Exception raised when rate limit is exceeded."""
    
    def __init__(self, message: str = "Rate limit exceeded"):
        super().__init__(message, status_code=429)


class DatabaseError(InquirySystemError):
    """Exception raised for database errors."""
    
    def __init__(self, message: str = "Database error"):
        super().__init__(message, status_code=500)


class ExternalServiceError(InquirySystemError):
    """Exception raised for external service errors."""
    
    def __init__(self, message: str = "External service error"):
        super().__init__(message, status_code=502)
