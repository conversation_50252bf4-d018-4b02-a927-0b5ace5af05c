"""
Main application entry point for the Inquiry System.

This module creates and configures the Flask application.
"""

import os
import sys
from pathlib import Path

# Add the backend/src directory to Python path
backend_src = Path(__file__).parent.parent
sys.path.insert(0, str(backend_src))

from inquiry_system.core import create_app

# Create the application
app = create_app()

if __name__ == '__main__':
    # Run the development server
    app.run(
        host='0.0.0.0',
        port=5123,
        debug=True
    )
