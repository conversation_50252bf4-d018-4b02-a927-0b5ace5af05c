"""
Configuration for the Inquiry System.

This module contains the production configuration.
"""

import os
import secrets
from pathlib import Path


class Config:
    """Production configuration class."""
    
    # Application settings
    APP_NAME = "Inquiry System"
    APP_VERSION = "2.0.0"
    
    # Security settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or secrets.token_urlsafe(32)
    WTF_CSRF_ENABLED = False  # Disable CSRF for API endpoints
    WTF_CSRF_TIME_LIMIT = 3600  # 1 hour
    
    # Database settings
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        f'sqlite:///{Path(__file__).parent.parent.parent.parent.parent}/instance/inquiry.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_RECORD_QUERIES = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'connect_args': {
            'timeout': 30,
            'check_same_thread': False
        }
    }
    
    # Session settings
    PERMANENT_SESSION_LIFETIME = 3600  # 1 hour
    SESSION_COOKIE_SECURE = False  # Set to True in production with HTTPS
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # File upload settings
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    UPLOAD_FOLDER = 'uploads'
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'xlsx', 'xls', 'csv'}
    
    # Pagination settings
    ITEMS_PER_PAGE = 50
    MAX_ITEMS_PER_PAGE = 100
    
    # Cache settings
    CACHE_TYPE = 'simple'
    CACHE_DEFAULT_TIMEOUT = 300  # 5 minutes
    
    # Logging settings
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'
    
    # Backup settings
    BACKUP_ENABLED = os.environ.get('BACKUP_ENABLED', 'true').lower() == 'true'
    BACKUP_RETENTION_DAYS = int(os.environ.get('BACKUP_RETENTION_DAYS', '90'))
    
    # Rate limiting settings
    RATELIMIT_STORAGE_URL = 'memory://'
    RATELIMIT_DEFAULT = '100 per hour'
    
    # Email settings (for future use)
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = int(os.environ.get('MAIL_PORT', '587'))
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() == 'true'
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # Timezone settings
    TIMEZONE = os.environ.get('TIMEZONE', 'UTC')
    
    @staticmethod
    def get_project_root() -> Path:
        """Get the project root directory."""
        return Path(__file__).parent.parent.parent.parent.parent
    
    @staticmethod
    def get_instance_path() -> Path:
        """Get the instance directory path."""
        return Config.get_project_root() / 'instance'
    
    @staticmethod
    def get_upload_path() -> Path:
        """Get the upload directory path."""
        return Config.get_instance_path() / 'uploads'
    
    @staticmethod
    def get_backup_path() -> Path:
        """Get the backup directory path."""
        return Config.get_instance_path() / 'backups'
    
    @classmethod
    def init_app(cls, app):
        """Initialize application with this configuration."""
        # Ensure instance directories exist
        cls.get_instance_path().mkdir(parents=True, exist_ok=True)
        cls.get_upload_path().mkdir(parents=True, exist_ok=True)
        cls.get_backup_path().mkdir(parents=True, exist_ok=True)
        
        # Set up logging
        import logging
        logging.basicConfig(
            level=getattr(logging, cls.LOG_LEVEL),
            format=cls.LOG_FORMAT,
            datefmt=cls.LOG_DATE_FORMAT
        )
