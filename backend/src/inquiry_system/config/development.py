"""
Development configuration for the Inquiry System.
"""

import os
from .base import BaseConfig


class DevelopmentConfig(BaseConfig):
    """Development configuration."""
    
    # Debug settings
    DEBUG = True
    TESTING = False
    
    # Database settings
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or \
        f'sqlite:///{BaseConfig.get_instance_path()}/inquiry_dev.db'
    SQLALCHEMY_ECHO = True  # Log all SQL statements
    
    # Security settings (relaxed for development)
    WTF_CSRF_ENABLED = False  # Disable CSRF for easier API testing
    SESSION_COOKIE_SECURE = False  # Allow HTTP cookies in development
    
    # Cache settings
    CACHE_TYPE = 'simple'
    
    # Logging settings
    LOG_LEVEL = 'DEBUG'
    
    # Development-specific settings
    SEND_FILE_MAX_AGE_DEFAULT = 0  # Disable caching for static files
    TEMPLATES_AUTO_RELOAD = True
    
    # Rate limiting (more permissive)
    RATELIMIT_DEFAULT = '1000 per hour'
    
    # Development tools
    FLASK_ENV = 'development'
    
    @classmethod
    def init_app(cls, app):
        """Initialize development-specific settings."""
        super().init_app(app)
        
        # Enable Flask debug toolbar if available
        try:
            from flask_debugtoolbar import DebugToolbarExtension
            toolbar = DebugToolbarExtension()
            toolbar.init_app(app)
        except ImportError:
            pass
        
        # Set up development logging
        import logging
        logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)
        
        print(f"🚀 Development server starting...")
        print(f"📁 Instance path: {cls.get_instance_path()}")
        print(f"🗄️  Database: {cls.SQLALCHEMY_DATABASE_URI}")
        print(f"🔧 Debug mode: {cls.DEBUG}")
