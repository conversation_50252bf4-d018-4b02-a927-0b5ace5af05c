"""
Configuration module for the Inquiry System.

This module contains configuration classes for different environments.
"""

import os
from .base import BaseConfig
from .config import Config
from .development import DevelopmentConfig
from .testing import TestingConfig


def get_config(env_name=None):
    """
    Get configuration class based on environment name.

    Args:
        env_name: Environment name ('development', 'testing', 'production')

    Returns:
        Configuration class for the specified environment
    """
    if env_name is None:
        env_name = os.environ.get('FLASK_ENV', 'development')

    config_map = {
        'development': DevelopmentConfig,
        'testing': TestingConfig,
        'production': Config,
    }

    return config_map.get(env_name, DevelopmentConfig)


__all__ = [
    "BaseConfig",
    "Config",
    "DevelopmentConfig",
    "TestingConfig",
    "get_config",
]
