"""
Testing configuration for the Inquiry System.
"""

import os
import tempfile
from .base import BaseConfig


class TestingConfig(BaseConfig):
    """Testing configuration."""
    
    # Testing settings
    TESTING = True
    DEBUG = False
    
    # Database settings (use in-memory SQLite for tests)
    SQLALCHEMY_DATABASE_URI = os.environ.get('TEST_DATABASE_URL') or 'sqlite:///:memory:'
    SQLALCHEMY_ECHO = False  # Reduce noise in test output
    
    # Security settings (disabled for testing)
    WTF_CSRF_ENABLED = False
    SECRET_KEY = 'test-secret-key'
    
    # Session settings
    SESSION_COOKIE_SECURE = False
    
    # Cache settings
    CACHE_TYPE = 'null'  # Disable caching for tests
    
    # Logging settings
    LOG_LEVEL = 'WARNING'  # Reduce log noise during tests
    
    # Rate limiting (disabled for tests)
    RATELIMIT_ENABLED = False
    
    # File upload settings (use temporary directory)
    UPLOAD_FOLDER = tempfile.mkdtemp()
    
    # Backup settings (disabled for tests)
    BACKUP_ENABLED = False
    
    # Test-specific settings
    PRESERVE_CONTEXT_ON_EXCEPTION = False
    
    @classmethod
    def init_app(cls, app):
        """Initialize testing-specific settings."""
        super().init_app(app)
        
        # Disable logging during tests except for errors
        import logging
        logging.disable(logging.WARNING)
        
        # Set up test database
        with app.app_context():
            from inquiry_system.core.database import db
            db.create_all()
        
        print("🧪 Test environment initialized")
