"""
Database initialization and configuration for the Inquiry System.

This module handles database setup, migrations, and connection management.
"""

from flask import Flask
from flask_migrate import Migrate
from flask_sqlalchemy import SQLAlchemy

# Initialize SQLAlchemy instance
db = SQLAlchemy()

# Initialize Flask-Migrate
migrate = Migrate()


def init_database(app: Flask) -> None:
    """Initialize database with the Flask application."""
    # Initialize SQLAlchemy with app
    db.init_app(app)
    
    # Initialize Flask-Migrate
    migrate.init_app(app, db)
    
    # Import all models to ensure they are registered
    from inquiry_system.models import (
        Customer,
        ProductInquiry,
        SerialNumber,
        Supplier,
        SupplierQuote,
        Order,
        Inventory,
        InventorySerialNumber,
        User,
        UserActivityLog,
    )
    
    # Create tables in development/testing if they don't exist
    if app.config.get('FLASK_ENV') in ['development', 'testing']:
        with app.app_context():
            db.create_all()


def create_tables() -> None:
    """Create all database tables."""
    db.create_all()


def drop_tables() -> None:
    """Drop all database tables."""
    db.drop_all()


def reset_database() -> None:
    """Reset the database by dropping and recreating all tables."""
    drop_tables()
    create_tables()


# Export the db instance for use in other modules
__all__ = ['db', 'migrate', 'init_database', 'create_tables', 'drop_tables', 'reset_database']
