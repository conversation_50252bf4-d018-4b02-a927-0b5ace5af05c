"""
Application factory for the Inquiry System.

This module contains the Flask application factory function.
"""

import os
from flask import Flask
from typing import Op<PERSON>

from inquiry_system.config import get_config
from inquiry_system.core.database import init_database
from inquiry_system.core.extensions import init_extensions


def create_app(config_name: Optional[str] = None) -> Flask:
    """
    Create and configure the Flask application.
    
    Args:
        config_name: Configuration environment name ('development', 'testing', 'production')
        
    Returns:
        Configured Flask application instance
    """
    # Create Flask application
    app = Flask(__name__)
    
    # Determine configuration
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    # Load configuration
    config_class = get_config(config_name)
    app.config.from_object(config_class)
    
    # Initialize configuration
    config_class.init_app(app)
    
    # Initialize database
    init_database(app)
    
    # Initialize extensions
    init_extensions(app)
    
    # Register blueprints
    register_blueprints(app)
    
    # Register error handlers
    register_error_handlers(app)
    
    # Register CLI commands
    register_cli_commands(app)
    
    # Set up logging
    setup_logging(app)
    
    return app


def register_blueprints(app: Flask) -> None:
    """Register application blueprints."""
    # Import blueprints
    from inquiry_system.api import api_bp

    # Register API blueprints
    app.register_blueprint(api_bp, url_prefix='/api')

    # Register frontend blueprint (for serving templates)
    from inquiry_system.frontend import frontend_bp
    app.register_blueprint(frontend_bp)


def register_error_handlers(app: Flask) -> None:
    """Register application error handlers."""
    from flask import jsonify, request
    from werkzeug.exceptions import HTTPException
    from inquiry_system.utils.exceptions import InquirySystemError
    
    @app.errorhandler(HTTPException)
    def handle_http_exception(error):
        """Handle HTTP exceptions."""
        if request.is_json or request.path.startswith('/api/'):
            return jsonify({
                'error': error.description,
                'status_code': error.code
            }), error.code
        else:
            # For non-API requests, use default error handling
            return error
    
    @app.errorhandler(InquirySystemError)
    def handle_inquiry_system_error(error):
        """Handle custom application errors."""
        return jsonify({
            'error': str(error),
            'status_code': error.status_code
        }), error.status_code
    
    @app.errorhandler(500)
    def handle_internal_error(error):
        """Handle internal server errors."""
        app.logger.error(f'Internal server error: {error}')
        if request.is_json or request.path.startswith('/api/'):
            return jsonify({
                'error': 'Internal server error',
                'status_code': 500
            }), 500
        else:
            return error


def register_cli_commands(app: Flask) -> None:
    """Register CLI commands."""
    from inquiry_system.cli import init_db_command, create_user_command
    
    app.cli.add_command(init_db_command)
    app.cli.add_command(create_user_command)


def setup_logging(app: Flask) -> None:
    """Set up application logging."""
    if not app.debug and not app.testing:
        import logging
        from logging.handlers import RotatingFileHandler
        
        # Create logs directory if it doesn't exist
        log_dir = app.config.get('LOG_DIR', 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        # Set up file handler
        file_handler = RotatingFileHandler(
            os.path.join(log_dir, 'inquiry_system.log'),
            maxBytes=10240000,  # 10MB
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s %(name)s %(threadName)s : %(message)s'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        
        app.logger.setLevel(logging.INFO)
        app.logger.info('Inquiry System startup')
