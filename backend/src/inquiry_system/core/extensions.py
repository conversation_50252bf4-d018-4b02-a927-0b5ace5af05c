"""
Flask extensions initialization for the Inquiry System.

This module handles the initialization of all Flask extensions.
"""

from flask import Flask
from flask_cors import CORS
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>
from flask_wtf.csrf import CSRFProtect
from flask_caching import Cache
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

# Initialize extensions
cors = CORS()
login_manager = LoginManager()
csrf = CSRFProtect()
cache = Cache()
limiter = Limiter(key_func=get_remote_address)


def init_extensions(app: Flask) -> None:
    """Initialize all Flask extensions with the application."""
    
    # Initialize CORS
    cors.init_app(app, resources={
        r"/api/*": {
            "origins": ["http://localhost:3000", "http://127.0.0.1:3000"],
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization", "X-Requested-With"]
        }
    })
    
    # Initialize Login Manager
    login_manager.init_app(app)
    login_manager.login_view = 'api.login'
    login_manager.login_message = '请先登录'
    login_manager.login_message_category = 'info'
    login_manager.session_protection = 'strong'
    
    # Initialize CSRF Protection
    csrf.init_app(app)
    
    # Initialize Cache
    cache.init_app(app)
    
    # Initialize Rate Limiter
    limiter.init_app(app)
    
    # Set up user loader for Flask-Login
    @login_manager.user_loader
    def load_user(user_id):
        from inquiry_system.models.user import User
        return User.query.get(int(user_id))
    
    # Set up unauthorized handler
    @login_manager.unauthorized_handler
    def unauthorized():
        from flask import jsonify, request
        if request.is_json or request.path.startswith('/api/'):
            return jsonify({'error': 'Authentication required'}), 401
        else:
            from flask import redirect, url_for
            return redirect(url_for('frontend.login'))


# Export extensions for use in other modules
__all__ = [
    'cors',
    'login_manager', 
    'csrf',
    'cache',
    'limiter',
    'init_extensions'
]
