"""
Frontend module for the Inquiry System.

This module handles serving frontend templates and static files.
"""

from flask import Blueprint, render_template, current_app, redirect, url_for
from flask_login import login_required, current_user

# Create frontend blueprint
frontend_bp = Blueprint(
    'frontend',
    __name__,
    template_folder='../../../frontend/templates',
    static_folder='../../../frontend/static',
    static_url_path='/static'
)


@frontend_bp.route('/')
@login_required
def index():
    """Main dashboard page."""
    # Define all systems and their permission requirements
    all_systems = [
        # User management system, operation audit system (admin only)
        [
            {'name': '用户管理系统', 'url': '/user-management/', 'admin_only': True, 'color_class': 'primary-system'},
            {'name': '操作审计系统', 'url': '/audit/', 'admin_only': True, 'color_class': 'audit-system'}
        ],

        # Customer management system (admin only)
        [{'name': '客户管理系统', 'url': '/customer/', 'admin_only': True, 'color_class': 'data-system'}],

        # Product inquiry related systems
        [
            {'name': '产品咨询管理系统', 'url': '/inquiry/', 'admin_only': False, 'color_class': 'inquiry-system'},
            {'name': '产品咨询展示系统', 'url': '/inquiry-display/', 'admin_only': True, 'color_class': 'inquiry-system'}
        ],

        # Order related systems (admin only)
        [
            {'name': '产品下单管理系统', 'url': '/order/', 'admin_only': True, 'color_class': 'order-system'},
            {'name': '产品下单展示系统', 'url': '/order-display/', 'admin_only': True, 'color_class': 'order-system'}
        ],

        # Inventory related systems
        [
            {'name': '出入库管理系统', 'url': '/inventory/', 'admin_only': False, 'color_class': 'data-system'},
            {'name': '出库清单管理', 'url': '/outbound/', 'admin_only': False, 'color_class': 'data-system'}
        ]
    ]

    # Filter systems based on user permissions
    filtered_systems = []
    for row in all_systems:
        filtered_row = [
            system for system in row
            if not system['admin_only'] or current_user.is_admin
        ]
        if filtered_row:  # Only add non-empty rows
            filtered_systems.append(filtered_row)

    return render_template('index.html', systems=filtered_systems)


@frontend_bp.route('/login')
def login():
    """Login page."""
    if current_user.is_authenticated:
        return redirect(url_for('frontend.index'))
    return render_template('auth/login.html')


@frontend_bp.route('/register')
def register():
    """Registration page."""
    if current_user.is_authenticated:
        return redirect(url_for('frontend.index'))
    return render_template('auth/register.html')


# Route handlers for different modules
@frontend_bp.route('/customer/')
@login_required
def customer_index():
    """Customer management page."""
    return render_template('customer/index.html')

@frontend_bp.route('/inquiry/')
@login_required
def inquiry_index():
    """Inquiry management page."""
    is_admin = False
    if hasattr(current_user, 'is_admin'):
        is_admin = current_user.is_admin
    return render_template('inquiry/index.html', is_admin=is_admin)

@frontend_bp.route('/inquiry/manage-quotes')
@login_required
def inquiry_manage_quotes():
    """Inquiry quotes management page."""
    return render_template('inquiry/manage_quotes.html')

@frontend_bp.route('/inquiry-display/')
@login_required
def inquiry_display_index():
    """Inquiry display page."""
    return render_template('inquiry_display/index.html')

@frontend_bp.route('/order/')
@login_required
def order_index():
    """Order management page."""
    return render_template('order/index.html')

@frontend_bp.route('/order-display/')
@login_required
def order_display_index():
    """Order display page."""
    return render_template('order_display/index.html')

@frontend_bp.route('/inventory/')
@login_required
def inventory_index():
    """Inventory management page."""
    return render_template('inventory/index.html')

@frontend_bp.route('/outbound/')
@login_required
def outbound_index():
    """Outbound management page."""
    return render_template('outbound/index.html')

@frontend_bp.route('/user-management/')
@login_required
def user_management_index():
    """User management page."""
    return render_template('user_management/index.html')

@frontend_bp.route('/audit/')
@login_required
def audit_index():
    """Audit page."""
    return render_template('audit/index.html')

@frontend_bp.route('/forex/')
@login_required
def forex_index():
    """Forex page."""
    return render_template('forex/index.html')


# Add context processors for templates
@frontend_bp.app_context_processor
def inject_app_info():
    """Inject application information into templates."""
    return {
        'app_name': current_app.config.get('APP_NAME', 'Inquiry System'),
        'app_version': current_app.config.get('APP_VERSION', '2.0.0'),
        'environment': current_app.config.get('FLASK_ENV', 'production')
    }


__all__ = ['frontend_bp']
