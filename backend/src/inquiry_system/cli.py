"""
CLI commands for the Inquiry System.

This module contains Flask CLI commands for database management and user creation.
"""

import click
from flask import current_app
from flask.cli import with_appcontext

from inquiry_system.core.database import db, create_tables, drop_tables
from inquiry_system.models.user import User


@click.command('init-db')
@with_appcontext
def init_db_command():
    """Initialize the database."""
    click.echo('Initializing database...')
    
    # Drop existing tables
    drop_tables()
    
    # Create new tables
    create_tables()
    
    # Create default admin user
    admin_user = User(
        username='admin',
        email='<EMAIL>',
        is_admin=True
    )
    admin_user.set_password('admin123')  # Change this in production!
    
    db.session.add(admin_user)
    db.session.commit()
    
    click.echo('Database initialized successfully!')
    click.echo('Default admin user created:')
    click.echo('  Username: admin')
    click.echo('  Password: admin123')
    click.echo('  Please change the password after first login!')


@click.command('create-user')
@click.option('--username', prompt=True, help='Username for the new user')
@click.option('--email', prompt=True, help='Email for the new user')
@click.option('--password', prompt=True, hide_input=True, 
              confirmation_prompt=True, help='Password for the new user')
@click.option('--admin', is_flag=True, help='Make user an administrator')
@with_appcontext
def create_user_command(username, email, password, admin):
    """Create a new user."""
    try:
        # Check if user already exists
        if User.query.filter_by(username=username).first():
            click.echo(f'Error: User "{username}" already exists!')
            return
        
        if User.query.filter_by(email=email).first():
            click.echo(f'Error: Email "{email}" already exists!')
            return
        
        # Create new user
        user = User(
            username=username,
            email=email,
            is_admin=admin
        )
        user.set_password(password)
        
        db.session.add(user)
        db.session.commit()
        
        user_type = 'Administrator' if admin else 'Regular user'
        click.echo(f'{user_type} "{username}" created successfully!')
        
    except Exception as e:
        click.echo(f'Error creating user: {str(e)}')
        db.session.rollback()


@click.command('reset-db')
@click.confirmation_option(prompt='Are you sure you want to reset the database?')
@with_appcontext
def reset_db_command():
    """Reset the database (WARNING: This will delete all data!)."""
    click.echo('Resetting database...')
    
    # Drop all tables
    drop_tables()
    
    # Create new tables
    create_tables()
    
    click.echo('Database reset successfully!')
    click.echo('All data has been deleted!')


@click.command('list-users')
@with_appcontext
def list_users_command():
    """List all users."""
    users = User.query.all()
    
    if not users:
        click.echo('No users found.')
        return
    
    click.echo('Users:')
    click.echo('-' * 50)
    for user in users:
        user_type = 'Admin' if user.is_admin else 'User'
        click.echo(f'{user.id:3d} | {user.username:20s} | {user.email:30s} | {user_type}')


# Export commands
__all__ = [
    'init_db_command',
    'create_user_command', 
    'reset_db_command',
    'list_users_command'
]
