# Core Flask dependencies
Flask>=3.0.0,<4.0.0
Flask-SQLAlchemy>=3.1.1,<4.0.0
Flask-Migrate>=4.0.5,<5.0.0
Flask-Login>=0.6.3,<1.0.0
Flask-WTF>=1.2.1,<2.0.0
Flask-CORS>=4.0.0,<5.0.0

# Database
SQLAlchemy>=2.0.23,<3.0.0
alembic>=1.12.1,<2.0.0
psycopg2-binary>=2.9.7,<3.0.0

# Security
Werkzeug>=3.0.1,<4.0.0
cryptography>=41.0.0,<42.0.0

# Utilities
python-dateutil>=2.8.2,<3.0.0
requests>=2.31.0,<3.0.0
beautifulsoup4>=4.12.2,<5.0.0
num2words>=0.5.13,<1.0.0

# File processing
Pillow>=10.1.0,<11.0.0
openpyxl>=3.1.2,<4.0.0
XlsxWriter>=3.1.9,<4.0.0

# Background tasks
APScheduler>=3.11.0,<4.0.0
celery>=5.3.0,<6.0.0
redis>=5.0.0,<6.0.0

# Environment management
python-dotenv>=1.0.0,<2.0.0

# Caching
Flask-Caching>=2.1.0,<3.0.0

# Rate limiting
Flask-Limiter>=3.5.0,<4.0.0

# Validation
marshmallow>=3.20.0,<4.0.0
marshmallow-sqlalchemy>=0.29.0,<1.0.0
email-validator>=2.1.0,<3.0.0

# Monitoring and logging
structlog>=23.2.0,<24.0.0
prometheus-flask-exporter>=0.23.0,<1.0.0