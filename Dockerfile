# Multi-stage build for production-ready Docker image
# Stage 1: Build dependencies
FROM python:3.11-alpine AS builder

# Set build environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install build dependencies
RUN apk add --no-cache \
    build-base \
    sqlite-dev \
    gcc \
    musl-dev \
    libffi-dev

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy and install Python dependencies
COPY requirements.txt .
RUN pip install --upgrade pip && \
    pip install -r requirements.txt

# Stage 2: Production image
FROM python:3.11-alpine AS production

# Set production environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    FLASK_APP=app.py \
    FLASK_ENV=production \
    PATH="/opt/venv/bin:$PATH" \
    DATABASE_URL=sqlite:///instance/quotation.db \
    SECRET_KEY="" \
    BACKUP_ENABLED=true \
    LOG_LEVEL=INFO

# Install runtime dependencies only
RUN apk add --no-cache \
    sqlite \
    curl \
    bash \
    && rm -rf /var/cache/apk/*

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv

# Create application user and group
RUN addgroup -S appuser && \
    adduser -S -G appuser -u 1000 appuser

# Create application directory structure
WORKDIR /app

# Create necessary directories with proper permissions
RUN mkdir -p \
    /app/instance \
    /app/database_backups \
    /app/logs \
    /app/static/uploads && \
    chown -R appuser:appuser /app && \
    chmod -R 755 /app && \
    chmod -R 777 /app/instance /app/database_backups /app/logs /app/static/uploads

# Copy application code
COPY --chown=appuser:appuser . .

# Create database initialization script
COPY --chown=appuser:appuser <<EOF /app/init_database.py
#!/usr/bin/env python3
"""
Safe database initialization and migration script for Docker deployment
"""
import os
import sys
from pathlib import Path
import sqlite3

# Add the app directory to Python path
sys.path.insert(0, '/app')

from app import app, db
from models import SerialNumber, ProductInquiry, SupplierQuote, Supplier, Customer, User, Order, Inventory, UserActivityLog

def check_database_exists():
    """Check if database file exists and has content"""
    db_path = '/app/instance/quotation.db'
    if not os.path.exists(db_path):
        return False

    # Check if database has tables (not just an empty file)
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        conn.close()
        return len(tables) > 0
    except Exception:
        return False

def get_existing_tables():
    """Get list of existing tables in the database"""
    try:
        db_path = '/app/instance/quotation.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        conn.close()
        return tables
    except Exception as e:
        print(f"Error getting existing tables: {str(e)}")
        return []

def table_exists(table_name):
    """Check if a specific table exists"""
    try:
        db_path = '/app/instance/quotation.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?;", (table_name,))
        result = cursor.fetchone()
        conn.close()
        return result is not None
    except Exception:
        return False

def run_migrations():
    """Run database migrations for existing database"""
    try:
        with app.app_context():
            print("Running database migrations...")

            # Check for SerialNumber table and create if missing
            if not table_exists('serial_number'):
                print("Creating SerialNumber table...")
                # Create only the SerialNumber table
                SerialNumber.__table__.create(db.engine, checkfirst=True)
                print("SerialNumber table created successfully!")
            else:
                print("SerialNumber table already exists, skipping...")

            # Add any future migrations here
            # Example:
            # if not table_exists('new_table'):
            #     print("Creating new_table...")
            #     NewModel.__table__.create(db.engine, checkfirst=True)

            # Verify all expected tables exist
            expected_tables = [
                'customer', 'product_inquiry', 'supplier', 'supplier_quote',
                'order', 'inventory', 'user', 'user_activity_log', 'serial_number'
            ]

            missing_tables = []
            for table in expected_tables:
                if not table_exists(table):
                    missing_tables.append(table)

            if missing_tables:
                print(f"Creating missing tables: {missing_tables}")
                # Create all missing tables
                db.create_all()

            # Ensure default users exist (even in existing databases)
            create_default_users()

            print("Database migrations completed successfully!")
            return True

    except Exception as e:
        print(f"Database migration failed: {str(e)}")
        return False

def create_default_users():
    """Create default admin and user accounts"""
    try:
        with app.app_context():
            print("Creating default users...")

            # Default users to create
            default_users = [
                {'username': 'admin', 'password': 'szbk0755', 'is_admin': True},
                {'username': 'user', 'password': 'test123', 'is_admin': False}
            ]

            for user_data in default_users:
                username = user_data['username']

                # Check if user already exists
                existing_user = User.query.filter_by(username=username).first()
                if existing_user:
                    print(f"User '{username}' already exists, skipping...")
                    continue

                # Create new user
                user = User(
                    username=username,
                    is_admin=user_data['is_admin']
                )
                user.set_password(user_data['password'])
                db.session.add(user)

                role = "Administrator" if user_data['is_admin'] else "Regular user"
                print(f"Created {role}: {username}")

            db.session.commit()
            print("Default users creation completed!")
            return True

    except Exception as e:
        print(f"Default users creation failed: {str(e)}")
        return False

def init_fresh_database():
    """Initialize a fresh database with all tables"""
    try:
        with app.app_context():
            print("Initializing fresh database...")

            # Create all tables
            db.create_all()

            # Create default users
            create_default_users()

            print("Fresh database initialization completed successfully!")
            return True

    except Exception as e:
        print(f"Fresh database initialization failed: {str(e)}")
        return False

def check_database_connection():
    """Test database connectivity"""
    try:
        db_path = '/app/instance/quotation.db'
        conn = sqlite3.connect(db_path)
        conn.execute('SELECT 1')
        conn.close()
        print("Database connectivity check passed!")
        return True
    except Exception as e:
        print(f"Database connectivity check failed: {str(e)}")
        return False

def main():
    """Main database initialization logic"""
    db_exists = check_database_exists()

    if db_exists:
        print("Existing database detected. Running migrations...")
        existing_tables = get_existing_tables()
        print(f"Existing tables: {existing_tables}")
        success = run_migrations()
    else:
        print("No existing database found. Creating fresh database...")
        success = init_fresh_database()

    if success:
        success = check_database_connection()

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
EOF

# Create health check script
COPY --chown=appuser:appuser <<EOF /app/healthcheck.py
#!/usr/bin/env python3
"""
Health check script for Docker container
"""
import sys
import sqlite3
import requests
import time

def check_database():
    """Check database connectivity"""
    try:
        conn = sqlite3.connect('/app/instance/quotation.db')
        conn.execute('SELECT 1')
        conn.close()
        return True
    except Exception as e:
        print(f"Database health check failed: {e}")
        return False

def check_web_server():
    """Check if web server is responding"""
    try:
        response = requests.get('http://localhost:5123/health', timeout=5)
        return response.status_code == 200
    except Exception as e:
        print(f"Web server health check failed: {e}")
        return False

if __name__ == "__main__":
    # Wait a moment for services to start
    time.sleep(2)

    db_ok = check_database()
    web_ok = check_web_server()

    if db_ok and web_ok:
        print("Health check passed!")
        sys.exit(0)
    else:
        print("Health check failed!")
        sys.exit(1)
EOF

# Create startup script
COPY --chown=appuser:appuser <<EOF /app/startup.sh
#!/bin/bash
set -e

echo "Starting Product Inquiry System..."

# Initialize database if it doesn't exist
if [ ! -f "/app/instance/quotation.db" ]; then
    echo "Database not found. Initializing..."
    python /app/init_database.py
    if [ $? -ne 0 ]; then
        echo "Database initialization failed!"
        exit 1
    fi
else
    echo "Database exists. Checking for schema updates..."
    python /app/init_database.py
fi

# Ensure proper permissions
chmod 666 /app/instance/quotation.db 2>/dev/null || true

echo "Starting Flask application..."
exec python app.py
EOF

# Make scripts executable
RUN chmod +x /app/init_database.py /app/healthcheck.py /app/startup.sh

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 5123

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD python /app/healthcheck.py || exit 1

# Use startup script as entrypoint
CMD ["/app/startup.sh"]