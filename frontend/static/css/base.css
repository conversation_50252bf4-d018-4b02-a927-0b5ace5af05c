/* 卡片样式 */
.card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: none;
    transition: box-shadow 0.3s ease;
    background: #fff;
    border-radius: 8px;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-body {
    padding: 1.75rem;
    position: relative;
}

.card-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
}

/* 表单标签样式 */
.form-label {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.form-label.required:after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

/* 输入框样式 */
.form-control {
    border: 1px solid #ced4da;
    padding: 0.625rem 0.75rem;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
}

.form-control:hover {
    border-color: #b3d7ff;
}

/* 下拉选择框样式 */
.form-select {
    padding: 0.625rem 2.25rem 0.625rem 0.75rem;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
}

.form-select:hover {
    border-color: #b3d7ff;
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.btn-outline-primary {
    border-width: 2px;
}

.btn-outline-danger {
    border-width: 2px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-sm {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

/* 输入组样式 */
.input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
    color: #495057;
    font-weight: 500;
    transition: all 0.3s ease;
}

.input-group:focus-within .input-group-text {
    border-color: #80bdff;
    background-color: #e9ecef;
}

/* 表单文本样式 */
.form-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* 错误提示样式 */
.invalid-feedback {
    display: block;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card-body {
        padding: 1.25rem;
    }
} 