/* 卡片样式 */
.card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: none;
    transition: box-shadow 0.3s ease;
    background: #fff;
    border-radius: 8px;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-body {
    padding: 1.75rem;
    position: relative;
}

.card-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
}

/* 搜索框容器 */
.search-container {
    position: relative;
    margin-bottom: 1rem;
}

/* 搜索框样式 */
.input-group {
    margin-bottom: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    transition: all 0.3s ease;
    width: 100%;
}

.input-group.show-results {
    border-radius: 4px 4px 0 0;
    border-bottom-color: transparent;
}

.input-group:focus-within {
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
}

/* 表单标签样式 */
.form-label {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.form-label.required:after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

/* 输入框样式 */
.form-control {
    border: 1px solid #ced4da;
    padding: 0.625rem 0.75rem;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
}

.form-control:hover {
    border-color: #b3d7ff;
}

/* 下拉选择框样式 */
.form-select {
    padding: 0.625rem 2.25rem 0.625rem 0.75rem;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
}

.form-select:hover {
    border-color: #b3d7ff;
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.btn-outline-primary {
    border-width: 2px;
}

.btn-outline-danger {
    border-width: 2px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-sm {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

/* 供应商报价样式 */
.supplier-quote {
    padding: 1.25rem;
    border-radius: 8px;
    background-color: #f8f9fa;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.supplier-quote:hover {
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 输入组样式 */
.input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
    color: #495057;
    font-weight: 500;
    transition: all 0.3s ease;
}

.input-group:focus-within .input-group-text {
    border-color: #80bdff;
    background-color: #e9ecef;
}

/* 表单文本样式 */
.form-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* 搜索结果样式 */
#searchResults {
    position: absolute;
    top: 100%;
    left: 0;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1050;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #e9ecef;
    border-top: none;
    border-radius: 0 0 4px 4px;
    background-color: white;
    margin-top: -1px;
}

#searchResults .list-group-item {
    cursor: pointer;
    border: none;
    border-bottom: 1px solid #e9ecef;
    padding: 12px 16px;
    transition: all 0.2s ease;
}

#searchResults .list-group-item:last-child {
    border-bottom: none;
}

#searchResults .list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(4px);
}

#searchResults .list-group-item h6 {
    margin: 0;
    color: #2c3e50;
    font-weight: 500;
}

#searchResults .list-group-item small {
    color: #6c757d;
}

#searchResults .list-group-item .supplier-info {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px dashed #e9ecef;
}

/* 滚动条样式 */
#searchResults::-webkit-scrollbar {
    width: 6px;
}

#searchResults::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#searchResults::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#searchResults::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 加载状态指示器 */
.loading {
    position: relative;
    pointer-events: none;
}

.loading:after {
    content: '';
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: translateY(-50%) rotate(0deg);
    }

    100% {
        transform: translateY(-50%) rotate(360deg);
    }
}

/* 序列号管理专用动画 */
@keyframes serialFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes serialPulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

@keyframes serialShimmer {
    0% {
        background-position: -200px 0;
    }

    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.serial-input-item {
    animation: serialFadeIn 0.5s ease-out;
}

.serial-input-item.filled .form-label::before {
    animation: serialPulse 0.6s ease-in-out;
}

.serial-batch-input-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: -200px;
    width: 200px;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: serialShimmer 2s infinite;
    pointer-events: none;
}

/* 错误提示样式 */
.invalid-feedback {
    display: block;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* 必填字段标记样式 */
.required:after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

/* 序列号管理模态框样式 */
.serial-number-modal .modal-dialog {
    max-width: 900px;
    margin: 1rem auto;
}

.serial-number-modal .modal-content {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.serial-number-modal .modal-header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
    color: white;
    border-bottom: none;
    padding: 2rem 2.5rem 1.5rem;
    position: relative;
}

.serial-number-modal .modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #fbbf24, #f59e0b, #d97706);
}

.serial-number-modal .modal-title {
    font-weight: 700;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
    letter-spacing: -0.025em;
}

.serial-number-modal .modal-title::before {
    content: "📋";
    font-size: 1.5rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem;
    border-radius: 0.75rem;
    backdrop-filter: blur(10px);
}

.serial-number-modal .btn-close {
    filter: brightness(0) invert(1);
    opacity: 0.9;
    font-size: 1.25rem;
    padding: 0.75rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.serial-number-modal .btn-close:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.serial-number-modal .modal-body {
    padding: 2.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    min-height: 500px;
}

/* 产品信息展示 */
.serial-product-info {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 1rem;
    padding: 1.75rem;
    margin-bottom: 2rem;
    border: 2px solid #e2e8f0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.serial-product-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4f46e5, #7c3aed, #ec4899);
}

.serial-product-info h6 {
    margin: 0;
    color: #1e293b;
    font-weight: 600;
    font-size: 1.125rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    letter-spacing: -0.025em;
}

.serial-product-info h6::before {
    content: "📦";
    font-size: 1.25rem;
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    padding: 0.5rem;
    border-radius: 0.75rem;
    filter: grayscale(0);
}

/* 序列号输入容器 */
.serial-inputs-container {
    max-height: 450px;
    overflow-y: auto;
    padding-right: 0.75rem;
    margin-right: -0.25rem;
}

.serial-inputs-container::-webkit-scrollbar {
    width: 8px;
}

.serial-inputs-container::-webkit-scrollbar-track {
    background: rgba(226, 232, 240, 0.5);
    border-radius: 10px;
    margin: 0.5rem 0;
}

.serial-inputs-container::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    border-radius: 10px;
    border: 2px solid transparent;
    background-clip: content-box;
}

.serial-inputs-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #4f46e5, #7c3aed);
    border-radius: 10px;
    border: 2px solid transparent;
    background-clip: content-box;
}

/* 序列号输入项 */
.serial-input-item {
    background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border: 2px solid #e2e8f0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.serial-input-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.serial-input-item:hover {
    border-color: #6366f1;
    box-shadow: 0 8px 32px rgba(99, 102, 241, 0.15);
    transform: translateY(-2px);
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.serial-input-item:hover::before {
    opacity: 1;
}

.serial-input-item:focus-within {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    transform: translateY(-2px);
}

.serial-input-item:focus-within::before {
    opacity: 1;
}

/* 序列号标签 */
.serial-input-item .form-label {
    color: #1e293b;
    font-weight: 700;
    font-size: 1rem;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    letter-spacing: -0.025em;
}

.serial-input-item .form-label::before {
    content: "#";
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 800;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* 序列号输入框 */
.serial-number-input {
    border: 2px solid #e2e8f0;
    border-radius: 0.75rem;
    padding: 1rem 1.25rem;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    letter-spacing: 0.025em;
}

.serial-number-input:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    background: #ffffff;
    transform: translateY(-1px);
    outline: none;
}

.serial-number-input:hover {
    border-color: #6366f1;
    background: #ffffff;
    transform: translateY(-1px);
}

.serial-number-input::placeholder {
    color: #64748b;
    font-style: normal;
    font-family: system-ui, -apple-system, sans-serif;
    font-weight: 400;
}

/* 已填写状态 */
.serial-input-item.filled {
    border-color: #10b981;
    background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
    box-shadow: 0 8px 32px rgba(16, 185, 129, 0.15);
}

.serial-input-item.filled::before {
    background: linear-gradient(135deg, #10b981, #059669);
    opacity: 1;
}

.serial-input-item.filled .form-label::before {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    content: "✓";
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.serial-input-item.filled .serial-number-input {
    border-color: #10b981;
    background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
    color: #065f46;
    font-weight: 600;
}

/* 模态框底部 */
.serial-number-modal .modal-footer {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-top: 2px solid #e2e8f0;
    padding: 2rem 2.5rem;
    border-radius: 0 0 1rem 1rem;
}

.serial-number-modal .modal-footer .btn {
    padding: 0.875rem 2rem;
    font-weight: 700;
    font-size: 1rem;
    border-radius: 0.75rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    letter-spacing: -0.025em;
    min-width: 120px;
}

.serial-number-modal .modal-footer .btn-primary {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    box-shadow: 0 8px 24px rgba(79, 70, 229, 0.3);
}

.serial-number-modal .modal-footer .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(79, 70, 229, 0.4);
    background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
}

.serial-number-modal .modal-footer .btn-secondary {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
    color: white;
    box-shadow: 0 8px 24px rgba(100, 116, 139, 0.3);
}

.serial-number-modal .modal-footer .btn-secondary:hover {
    background: linear-gradient(135deg, #475569 0%, #334155 100%);
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(100, 116, 139, 0.4);
}

/* 批量输入区域 */
.serial-batch-input-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 2px solid #e2e8f0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.serial-batch-input-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #10b981, #059669, #047857);
}

.serial-batch-input-section .form-label {
    color: #1e293b;
    font-weight: 700;
    font-size: 1.125rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    letter-spacing: -0.025em;
}

.serial-batch-input-section .form-label::before {
    content: "⚡";
    font-size: 1.25rem;
    background: linear-gradient(135deg, #10b981, #059669);
    padding: 0.5rem;
    border-radius: 0.75rem;
    filter: grayscale(0);
}

.serial-batch-textarea {
    border: 2px solid #e2e8f0;
    border-radius: 0.75rem;
    padding: 1rem 1.25rem;
    font-size: 0.95rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    resize: vertical;
    min-height: 100px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
    line-height: 1.6;
}

.serial-batch-textarea:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    background: #ffffff;
    transform: translateY(-1px);
}

.serial-batch-textarea:hover {
    border-color: #6366f1;
    transform: translateY(-1px);
}

.serial-batch-textarea::placeholder {
    color: #64748b;
    font-style: normal;
    font-family: system-ui, -apple-system, sans-serif;
    line-height: 1.5;
}

.serial-batch-actions {
    display: flex;
    gap: 0.75rem;
    margin-top: 1.25rem;
    flex-wrap: wrap;
}

.serial-batch-actions .btn {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 0.625rem 1.25rem;
    border-radius: 0.75rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    letter-spacing: -0.025em;
}

.serial-batch-actions .btn-outline-primary {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.35);
    position: relative;
    overflow: hidden;
}

.serial-batch-actions .btn-outline-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.serial-batch-actions .btn-outline-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 32px rgba(16, 185, 129, 0.5);
    background: linear-gradient(135deg, #059669, #047857);
}

.serial-batch-actions .btn-outline-primary:hover::before {
    left: 100%;
}

.serial-batch-actions .btn-outline-primary:active {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.serial-batch-actions .btn-outline-primary i {
    margin-right: 0.5rem;
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.serial-batch-actions .btn-outline-primary:hover i {
    transform: translateY(2px);
}

.serial-batch-actions .btn-outline-secondary {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    border: none;
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.35);
    position: relative;
    overflow: hidden;
}

.serial-batch-actions .btn-outline-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.serial-batch-actions .btn-outline-secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 32px rgba(245, 158, 11, 0.5);
    background: linear-gradient(135deg, #d97706, #b45309);
}

.serial-batch-actions .btn-outline-secondary:hover::before {
    left: 100%;
}

.serial-batch-actions .btn-outline-secondary:active {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
}

.serial-batch-actions .btn-outline-secondary i {
    margin-right: 0.5rem;
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.serial-batch-actions .btn-outline-secondary:hover i {
    transform: rotate(15deg);
}

/* 按钮加载状态 */
.serial-batch-actions .btn.loading {
    pointer-events: none;
    position: relative;
    color: transparent !important;
}

.serial-batch-actions .btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 按钮成功状态 */
.serial-batch-actions .btn.success {
    background: linear-gradient(135deg, #10b981, #059669) !important;
    color: white !important;
    position: relative;
}

.serial-batch-actions .btn.success::before {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.2rem;
    font-weight: bold;
    color: white;
    z-index: 1;
}

.serial-batch-actions .btn.success i,
.serial-batch-actions .btn.success span {
    opacity: 0;
}

/* 按钮文字效果 */
.serial-batch-actions .btn {
    font-weight: 700;
    letter-spacing: 0.025em;
    text-transform: none;
    position: relative;
    z-index: 1;
    min-width: 160px;
    justify-content: center;
    align-items: center;
    display: flex;
    gap: 0.5rem;
}

.serial-batch-actions .btn span {
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

/* 按钮脉冲效果 */
@keyframes buttonPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
    }
}

@keyframes buttonPulseOrange {
    0% {
        box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
    }
}

.serial-batch-actions .btn-outline-primary:focus {
    animation: buttonPulse 2s infinite;
}

.serial-batch-actions .btn-outline-secondary:focus {
    animation: buttonPulseOrange 2s infinite;
}

/* 图标旋转动画 */
@keyframes iconSpin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.serial-batch-actions .btn.loading i {
    animation: iconSpin 1s linear infinite;
}

/* 按钮禁用状态 */
.serial-batch-actions .btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none !important;
}

/* 成功状态动画 */
@keyframes successBounce {

    0%,
    20%,
    60%,
    100% {
        transform: translateY(0);
    }

    40% {
        transform: translateY(-10px);
    }

    80% {
        transform: translateY(-5px);
    }
}

.serial-batch-actions .btn.success {
    animation: successBounce 0.6s ease-in-out;
}

/* 空状态提示 */
.serial-empty-state {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.serial-empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}



/* 响应式调整 */
@media (max-width: 768px) {
    .card-body {
        padding: 1.25rem;
    }

    .supplier-quote {
        padding: 1rem;
    }

    .supplier-quote .row {
        margin-bottom: 0.75rem;
    }

    .supplier-quote .col-md-2 {
        margin-top: 0.75rem;
    }

    .serial-number-modal .modal-dialog {
        margin: 0.5rem;
        max-width: none;
    }

    .serial-number-modal .modal-header {
        padding: 1.5rem 1.25rem 1rem;
    }

    .serial-number-modal .modal-body {
        padding: 1.5rem 1.25rem;
        min-height: auto;
    }

    .serial-number-modal .modal-footer {
        padding: 1.25rem;
        flex-direction: column;
        gap: 0.75rem;
    }

    .serial-number-modal .modal-footer .btn {
        width: 100%;
        padding: 1rem;
    }

    .serial-number-modal .modal-title {
        font-size: 1.25rem;
    }

    .serial-input-item {
        padding: 1.25rem;
        margin-bottom: 0.75rem;
    }

    .serial-input-item .form-label {
        font-size: 0.95rem;
    }

    .serial-input-item .form-label::before {
        width: 24px;
        height: 24px;
        font-size: 0.8rem;
    }

    .serial-number-input {
        padding: 0.875rem 1rem;
        font-size: 0.95rem;
    }

    .serial-batch-input-section {
        padding: 1.5rem 1.25rem;
        margin-bottom: 1.5rem;
    }

    .serial-batch-input-section .form-label {
        font-size: 1rem;
    }

    .serial-batch-textarea {
        padding: 0.875rem 1rem;
        font-size: 0.9rem;
    }

    .serial-batch-actions {
        justify-content: stretch;
        flex-direction: column;
    }

    .serial-batch-actions .btn {
        width: 100%;
        padding: 1rem;
        min-width: auto;
        font-size: 0.95rem;
    }

    .serial-batch-actions .btn span {
        font-size: 0.9rem;
    }

    .serial-batch-actions .btn i {
        font-size: 1.1rem;
    }

    .serial-inputs-container {
        max-height: 350px;
        padding-right: 0.5rem;
    }
}