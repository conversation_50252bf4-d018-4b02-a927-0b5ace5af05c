.card {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border: none;
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

.form-control:focus {
    box-shadow: none;
    border-color: #0d6efd;
}

.password-strength {
    height: 4px;
    margin-top: 0.5rem;
    background-color: #e9ecef;
    border-radius: 2px;
}

.password-strength::before {
    content: '';
    display: block;
    height: 100%;
    width: 0;
    background-color: #dc3545;
    transition: width 0.3s, background-color 0.3s;
    border-radius: 2px;
}

.password-strength.weak::before {
    width: 33.33%;
    background-color: #dc3545;
}

.password-strength.medium::before {
    width: 66.66%;
    background-color: #ffc107;
}

.password-strength.strong::before {
    width: 100%;
    background-color: #198754;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card {
        margin: 1rem;
    }
} 