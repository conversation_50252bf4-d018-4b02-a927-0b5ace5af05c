{% extends "base.html" %}

{% block title %}用户管理系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>用户管理</h2>
        <div class="d-flex gap-2">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#userModal">
                <i class="bi bi-person-plus"></i> 新建用户
            </button>
            <a href="/" class="btn btn-outline-primary">
                <i class="bi bi-house-door"></i> 返回首页
            </a>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>用户名</th>
                    <th>管理员</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="userList"></tbody>
        </table>
    </div>
</div>

<!-- 用户编辑模态框 -->
<div class="modal fade" id="userModal" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新建用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="userForm">
                    <div class="mb-3">
                        <label class="form-label">用户名</label>
                        <input type="text" class="form-control" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">密码</label>
                        <input type="password" class="form-control" name="password" required>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" name="is_admin">
                        <label class="form-check-label">管理员权限</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveUser()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加修改密码模态框 -->
<div class="modal fade" id="changePasswordModal" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">修改密码</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    <input type="hidden" id="changePasswordUserId">
                    <div class="mb-3">
                        <label class="form-label">用户名</label>
                        <input type="text" class="form-control" id="changePasswordUsername" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">新密码</label>
                        <input type="password" class="form-control" id="newPassword" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">确认新密码</label>
                        <input type="password" class="form-control" id="confirmNewPassword" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="changePassword()">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
<style>
    .table {
        background-color: #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
    }
    .table thead th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        color: #2c3e50;
        font-weight: 600;
    }
    .table td {
        vertical-align: middle;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载时获取用户列表
document.addEventListener('DOMContentLoaded', loadUsers);

// 加载用户列表
async function loadUsers() {
    try {
        const response = await fetch('/user-management/users');
        const users = await response.json();
        
        const tbody = document.getElementById('userList');
        tbody.innerHTML = '';
        
        if (users.length === 0) {
            tbody.innerHTML = '<tr><td colspan="4" class="text-center">暂无用户</td></tr>';
            return;
        }
        
        users.forEach(user => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${user.username}</td>
                <td>${user.is_admin ? '是' : '否'}</td>
                <td>${formatDateTime(user.created_at)}</td>
                <td>
                    <button class="btn btn-primary btn-sm me-2" onclick="showChangePasswordDialog(${user.id}, '${user.username}')"
                            ${user.username === '{{ current_user.username }}' ? 'disabled' : ''}>
                        <i class="bi bi-key"></i> 修改密码
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="deleteUser(${user.id}, '${user.username}')" 
                            ${user.username === '{{ current_user.username }}' ? 'disabled' : ''}>
                        <i class="bi bi-trash"></i> 删除
                    </button>
                </td>
            `;
            tbody.appendChild(tr);
        });
    } catch (error) {
        console.error('加载用户列表失败:', error);
        showToast('error', '加载用户列表失败');
    }
}

// 保存用户
async function saveUser() {
    const form = document.getElementById('userForm');
    const formData = new FormData(form);
    
    const data = {
        username: formData.get('username'),
        password: formData.get('password'),
        is_admin: formData.get('is_admin') === 'on'
    };
    
    try {
        const response = await fetch('/user-management/users', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            showToast('success', '用户创建成功');
            bootstrap.Modal.getInstance(document.getElementById('userModal')).hide();
            form.reset();
            loadUsers();
        } else {
            showToast('error', result.error || '创建用户失败');
        }
    } catch (error) {
        console.error('创建用户失败:', error);
        showToast('error', '创建用户失败');
    }
}

// 删除用户
async function deleteUser(userId, username) {
    if (!confirm(`确定要删除用户 "${username}" 吗？`)) {
        return;
    }
    
    try {
        const response = await fetch(`/user-management/users/${userId}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (response.ok) {
            showToast('success', '用户删除成功');
            loadUsers();
        } else {
            showToast('error', result.error || '删除用户失败');
        }
    } catch (error) {
        console.error('删除用户失败:', error);
        showToast('error', '删除用户失败');
    }
}

// 格式化日期时间
function formatDateTime(dateStr) {
    if (!dateStr) return '-';
    return new Date(dateStr).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 显示修改密码对话框
function showChangePasswordDialog(userId, username) {
    document.getElementById('changePasswordUserId').value = userId;
    document.getElementById('changePasswordUsername').value = username;
    document.getElementById('newPassword').value = '';
    document.getElementById('confirmNewPassword').value = '';
    const modal = new bootstrap.Modal(document.getElementById('changePasswordModal'));
    modal.show();
}

// 修改密码
async function changePassword() {
    const userId = document.getElementById('changePasswordUserId').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmNewPassword').value;
    
    if (!newPassword || !confirmPassword) {
        showToast('warning', '请输入新密码');
        return;
    }
    
    if (newPassword !== confirmPassword) {
        showToast('warning', '两次输入的密码不一致');
        return;
    }
    
    try {
        const response = await fetch(`/user-management/users/${userId}/change-password`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                new_password: newPassword
            })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            showToast('success', '密码修改成功');
            const modal = bootstrap.Modal.getInstance(document.getElementById('changePasswordModal'));
            modal.hide();
        } else {
            showToast('error', result.error || '修改密码失败');
        }
    } catch (error) {
        console.error('修改密码失败:', error);
        showToast('error', '修改密码失败');
    }
}
</script>
{% endblock %} 