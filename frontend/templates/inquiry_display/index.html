{% extends "base.html" %}

{% block title %}产品咨询展示系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>产品咨询展示系统</h2>
        <a href="/" class="btn btn-outline-primary">
            <i class="bi bi-house-door"></i> 返回首页
        </a>
    </div>
    
    <div class="card mb-4">
        <div class="card-body">
            <form id="searchForm" class="row g-3">
                <div class="col-md-4">
                    <label for="customerSelect" class="form-label">选择客户</label>
                    <select id="customerSelect" class="form-select" required>
                        <option value="">选择客户...</option>
                    </select>
                </div>
                
                <div class="col-md-4">
                    <label for="dateRange" class="form-label">日期区间</label>
                    <div class="input-group">
                        <input type="date" id="startDate" class="form-control" required>
                        <span class="input-group-text">至</span>
                        <input type="date" id="endDate" class="form-control" required>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <label class="form-label">快捷选择</label>
                    <div class="btn-group w-100">
                        <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('today')">今天</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('7days')">近7天</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('14days')">近14天</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="setDateRange('30days')">近1个月</button>
                    </div>
                </div>
                
                <div class="col-12">
                    <div class="d-flex justify-content-end gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i> 查询
                        </button>
                        <button type="button" class="btn btn-success" onclick="downloadExcel()">
                            <i class="bi bi-download"></i> 下载Excel
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-bordered">
            <thead>
                <tr>
                    <th>日期</th>
                    <th>产品名称</th>
                    <th>数量</th>
                    <th>我的报价</th>
                    <th>预计交货天数</th>
                    <th>供应商报价</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="inquiryTableBody">
                <!-- 数据将通过JavaScript动态填充 -->
            </tbody>
        </table>
    </div>
</div>

<!-- 添加修改报价的模态框 -->
<div class="modal fade" id="editPriceModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">修改我的报价</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="editInquiryId">
                <div class="mb-3">
                    <label for="newPrice" class="form-label">新报价</label>
                    <div class="input-group">
                        <span class="input-group-text">¥</span>
                        <input type="number" id="newPrice" class="form-control" step="0.01" min="0" required>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateMyPrice()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加修改交货周期的模态框 -->
<div class="modal fade" id="editDeliveryDaysModal" tabindex="-1" aria-labelledby="editDeliveryDaysModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editDeliveryDaysModalLabel">修改交货周期</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="editInquiryId">
                <div class="mb-3">
                    <label for="newDays" class="form-label">新交货周期（天）</label>
                    <input type="number" class="form-control" id="newDays" min="0">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateDeliveryDays()">保存</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
<link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
<style>
    .supplier-quotes {
        margin: 0;
        padding: 0;
        list-style: none;
    }
    .supplier-quote {
        margin-bottom: 8px;
        padding: 4px;
        border: 1px solid #eee;
        border-radius: 4px;
    }
    .supplier-quote:last-child {
        margin-bottom: 0;
    }
    .supplier-info {
        margin-bottom: 4px;
    }
    .supplier-name {
        font-weight: bold;
        margin-right: 8px;
    }
    .supplier-contact {
        color: #666;
    }
    .quote-details {
        display: flex;
        justify-content: space-between;
        color: #333;
    }
    .price {
        color: #e74c3c;
        font-weight: bold;
    }
    .delivery-days {
        color: #3498db;
    }
    .table {
        background-color: #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
    }
    .table thead th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        color: #2c3e50;
        font-weight: 600;
    }
    .table td {
        vertical-align: middle;
    }
    .update-price-btn {
        cursor: pointer;
        color: #007bff;
        margin-left: 8px;
        transition: all 0.3s ease;
    }
    .update-price-btn:hover {
        color: #0056b3;
        transform: rotate(90deg);
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.33/moment-timezone.min.js"></script>
<script>
// 设置moment.js的默认时区为用户本地时区
moment.tz.setDefault(moment.tz.guess());

// 加载客户列表
async function loadCustomers() {
    try {
        const response = await fetch('/inquiry-display/customers');
        const customers = await response.json();
        const select = document.getElementById('customerSelect');
        
        customers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.id;
            option.textContent = customer.name;
            select.appendChild(option);
        });
        
        // 如果有客户，自动选择第一个并加载数据
        if (customers.length > 0) {
            select.value = customers[0].id;
            loadInquiries();
        }
        
        // 添加change事件监听器
        select.addEventListener('change', loadInquiries);
    } catch (error) {
        console.error('加载客户列表失败:', error);
    }
}

// 设置日期范围的函数
function setDateRange(range) {
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    const today = moment();
    
    // 设置结束日期为今天
    endDate.value = today.format('YYYY-MM-DD');
    
    // 根据选择设置开始日期
    switch(range) {
        case 'today':
            startDate.value = today.format('YYYY-MM-DD');
            break;
        case '7days':
            startDate.value = today.subtract(6, 'days').format('YYYY-MM-DD');
            break;
        case '14days':
            startDate.value = today.subtract(13, 'days').format('YYYY-MM-DD');
            break;
        case '30days':
            startDate.value = today.subtract(29, 'days').format('YYYY-MM-DD');
            break;
    }
}

// 页面加载时设置默认日期为今天
document.addEventListener('DOMContentLoaded', function() {
    loadCustomers();
    setDateRange('today');
    
    // 添加表单提交事件监听
    document.getElementById('searchForm').addEventListener('submit', function(e) {
        e.preventDefault();
        loadInquiries();
    });
});

// 修改加载询价记录的函数，添加日期范围参数
async function loadInquiries() {
    const customerId = document.getElementById('customerSelect').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    if (!customerId || !startDate || !endDate) {
        showToast('warning', '请选择客户和日期范围');
        return;
    }
    
    try {
        const response = await fetch(`/inquiry-display/inquiries?customer_id=${customerId}&start_date=${startDate}&end_date=${endDate}`);
        const inquiries = await response.json();
        
        const tbody = document.getElementById('inquiryTableBody');
        tbody.innerHTML = '';

        if (inquiries.length === 0) {
            const tr = document.createElement('tr');
            tr.innerHTML = '<td colspan="7" class="text-center">暂无数据</td>';
            tbody.appendChild(tr);
            return;
        }
        
        inquiries.forEach(inquiry => {
            const tr = document.createElement('tr');
            
            // 构建供应商报价HTML
            const supplierQuotesHtml = inquiry.supplier_quotes.map(quote => `
                <div class="supplier-quote">
                            <div class="supplier-info">
                                <span class="supplier-name">${quote.supplier_name}</span>
                        ${quote.supplier_contact ? `<span class="supplier-contact">${quote.supplier_contact}</span>` : ''}
                            </div>
                            <div class="quote-details">
                                <span class="price">¥${quote.price}</span>
                                <span class="delivery-days">${quote.delivery_days}天</span>
                            </div>
                </div>
            `).join('');
            
            tr.innerHTML = `
                <td>${moment(inquiry.inquiry_date).format('YYYY-MM-DD')}</td>
                <td>${inquiry.product_name}</td>
                <td>${inquiry.quantity}</td>
                <td>
                    ¥${inquiry.my_price}
                    <i class="bi bi-pencil-square update-price-btn" 
                       onclick="showEditPriceDialog('${inquiry.id}', ${inquiry.my_price})" 
                       title="修改报价"></i>
                </td>
                <td>${inquiry.expected_delivery_days}
                    <i class="bi bi-pencil-square update-price-btn" 
                       onclick="showEditDeliveryDaysDialog('${inquiry.id}', ${inquiry.expected_delivery_days})" 
                       title="修改交货周期"></i>
                </td>
                <td>${supplierQuotesHtml}</td>
                <td>
                    <button class="btn btn-outline-primary btn-sm" onclick="copyRowInfo(this)" title="复制信息">
                        <i class="bi bi-camera"></i> 报价
                    </button>
                </td>
            `;
            
            tbody.appendChild(tr);
        });
    } catch (error) {
        console.error('加载产品咨询记录失败:', error);
        showToast('error', '加载失败，请重试');
    }
}

// 修改下载Excel函数，添加日期范围参数
function downloadExcel() {
    const customerId = document.getElementById('customerSelect').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    if (!customerId || !startDate || !endDate) {
        showToast('warning', '请选择客户和日期范围');
        return;
    }
    
    window.location.href = `/inquiry-display/download?customer_id=${customerId}&start_date=${startDate}&end_date=${endDate}`;
}

// 添加复制功能
async function copyRowInfo(button) {
    const row = button.closest('tr');
    const productName = row.cells[1].textContent.trim();
    const quantity = row.cells[2].textContent.trim();
    const myPrice = row.cells[3].textContent.trim().split('\n')[0].trim(); // 只获取价格，不包括编辑按钮
    const deliveryDays = row.cells[4].textContent.trim().split('\n')[0].trim(); // 只获取天数，不包括编辑按钮
    
    const text = `产品名称：${productName}\n数量：${quantity}\n我的报价：${myPrice}\n预计交货天数：${deliveryDays}`;
    
    try {
        // 创建临时文本区域
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';  // 防止页面滚动
        textArea.style.opacity = '0';       // 隐藏元素
        document.body.appendChild(textArea);
        
        // 选择文本
        textArea.select();
        textArea.setSelectionRange(0, 99999); // 适用于移动设备
        
        // 尝试复制
        const successful = document.execCommand('copy');
        
        // 移除临时元素
        document.body.removeChild(textArea);
        
        if (successful) {
            showToast('success', '信息已复制到剪贴板');
        } else {
            throw new Error('复制失败');
        }
    } catch (err) {
        console.error('复制失败:', err);
        showToast('error', '复制失败，请手动复制');
        
        // 如果复制失败，至少显示信息让用户可以手动复制
        alert('请手动复制以下信息：\n\n' + text);
    }
}

// 显示修改报价对话框
function showEditPriceDialog(inquiryId, currentPrice) {
    document.getElementById('editInquiryId').value = inquiryId;
    document.getElementById('newPrice').value = currentPrice;
    const modal = new bootstrap.Modal(document.getElementById('editPriceModal'));
    modal.show();
}

// 更新我的报价
async function updateMyPrice() {
    const inquiryId = document.getElementById('editInquiryId').value;
    const newPrice = parseFloat(document.getElementById('newPrice').value);
    
    if (isNaN(newPrice) || newPrice < 0) {
        showToast('warning', '请输入有效的报价');
        return;
    }
    
    try {
        const response = await fetch(`/inquiry-display/inquiries/${inquiryId}/update_my_price`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                my_price: newPrice
            })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            showToast('success', '报价更新成功');
            const modal = bootstrap.Modal.getInstance(document.getElementById('editPriceModal'));
            modal.hide();
            loadInquiries(); // 重新加载列表
        } else {
            showToast('error', result.error || '更新失败');
        }
    } catch (error) {
        console.error('更新报价失败:', error);
        showToast('error', '更新失败，请重试');
    }
}

// 更新交货周期
async function updateDeliveryDays() {
    const inquiryId = document.getElementById('editInquiryId').value;
    const newDays = parseInt(document.getElementById('newDays').value);
    
    if (isNaN(newDays) || newDays < 0) {
        showToast('warning', '请输入有效的交货周期');
        return;
    }
    
    try {
        const response = await fetch(`/inquiry-display/inquiries/${inquiryId}/update_delivery_days`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                delivery_days: newDays
            })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            showToast('success', '交货周期更新成功');
            const modal = bootstrap.Modal.getInstance(document.getElementById('editDeliveryDaysModal'));
            modal.hide();
            loadInquiries(); // 重新加载列表
        } else {
            showToast('error', result.error || '更新失败');
        }
    } catch (error) {
        console.error('更新交货周期失败:', error);
        showToast('error', '更新失败，请重试');
    }
}

// 显示修改交货周期对话框
function showEditDeliveryDaysDialog(inquiryId, currentDays) {
    document.getElementById('editInquiryId').value = inquiryId;
    document.getElementById('newDays').value = currentDays;
    const modal = new bootstrap.Modal(document.getElementById('editDeliveryDaysModal'));
    modal.show();
}

</script>
{% endblock %} 