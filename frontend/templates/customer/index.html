{% extends "base.html" %}

{% block title %}客户管理{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>客户管理</h2>
        <div>
            <button type="button" class="btn btn-primary" onclick="showAddCustomerModal()">
                <i class="bi bi-plus-lg"></i> 新增客户
            </button>
            <a href="/" class="btn btn-outline-primary ms-2">
                <i class="bi bi-house-door"></i> 返回首页
            </a>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-bordered">
            <thead>
                <tr>
                    <th>客户ID</th>
                    <th>客户名称</th>
                    <th>地址</th>
                    <th>电话</th>
                    <th>联系人</th>
                    <th>国家</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="customerTableBody">
                <!-- 数据将通过JavaScript动态填充 -->
            </tbody>
        </table>
    </div>
</div>

<!-- 客户表单对话框 -->
<div class="modal fade" id="customerModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">新增客户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="customerForm">
                    <input type="hidden" id="customerId">
                    <div class="mb-3">
                        <label for="customerName" class="form-label">客户名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="customerName" required>
                    </div>
                    <div class="mb-3">
                        <label for="customerAddress" class="form-label">地址 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="customerAddress" required>
                    </div>
                    <div class="mb-3">
                        <label for="customerPhone" class="form-label">电话 <span class="text-danger">*</span></label>
                        <input type="tel" class="form-control" id="customerPhone" required>
                    </div>
                    <div class="mb-3">
                        <label for="customerContact" class="form-label">联系人 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="customerContact" required>
                    </div>
                    <div class="mb-3">
                        <label for="customerCountry" class="form-label">国家 <span class="text-danger">*</span></label>
                        <select class="form-select" id="customerCountry" required>
                            <option value="">选择国家...</option>
                            <!-- 国家选项将通过JavaScript动态填充 -->
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveCustomer()">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
<style>
    .table {
        background-color: #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
    }
    .table thead th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        color: #2c3e50;
        font-weight: 600;
    }
    .table td {
        vertical-align: middle;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
let customerModal = null;
let isEditing = false;

document.addEventListener('DOMContentLoaded', function() {
    customerModal = new bootstrap.Modal(document.getElementById('customerModal'));
    loadCustomers();
    loadCountries();
});

// 加载客户列表
async function loadCustomers() {
    try {
        const response = await fetch('/customer/list');
        const customers = await response.json();
        
        // 按客户ID升序排序
        customers.sort((a, b) => a.id - b.id);
        
        const tbody = document.getElementById('customerTableBody');
        tbody.innerHTML = '';
        
        if (customers.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center">暂无数据</td></tr>';
            return;
        }
        
        customers.forEach(customer => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${customer.id}</td>
                <td>${customer.name}</td>
                <td>${customer.address || '-'}</td>
                <td>${customer.phone || '-'}</td>
                <td>${customer.contact || '-'}</td>
                <td>${customer.country_name || '-'}</td>
                <td>
                    <button class="btn btn-outline-primary btn-sm" onclick="editCustomer(${customer.id})">
                        <i class="bi bi-pencil"></i> 编辑
                    </button>
                    <button class="btn btn-outline-danger btn-sm ms-2" onclick="deleteCustomer(${customer.id})">
                        <i class="bi bi-trash"></i> 删除
                    </button>
                </td>
            `;
            tbody.appendChild(tr);
        });
    } catch (error) {
        console.error('加载客户列表失败:', error);
        showToast('error', '加载客户列表失败');
    }
}

// 加载国家列表
async function loadCountries() {
    try {
        const response = await fetch('/customer/countries');
        const countries = await response.json();
        
        const select = document.getElementById('customerCountry');
        select.innerHTML = '<option value="">选择国家...</option>';
        
        countries.forEach(country => {
            const option = document.createElement('option');
            option.value = country.code;
            option.textContent = country.name;
            select.appendChild(option);
        });
    } catch (error) {
        console.error('加载国家列表失败:', error);
        showToast('error', '加载国家列表失败');
    }
}

// 显示新增客户对话框
function showAddCustomerModal() {
    isEditing = false;
    document.getElementById('modalTitle').textContent = '新增客户';
    document.getElementById('customerForm').reset();
    document.getElementById('customerId').value = '';
    customerModal.show();
}

// 编辑客户
async function editCustomer(customerId) {
    try {
        const response = await fetch(`/customer/list`);
        const customers = await response.json();
        const customer = customers.find(c => c.id === customerId);
        
        if (!customer) {
            showToast('error', '客户不存在');
            return;
        }
        
        isEditing = true;
        document.getElementById('modalTitle').textContent = '编辑客户';
        document.getElementById('customerId').value = customer.id;
        document.getElementById('customerName').value = customer.name;
        document.getElementById('customerAddress').value = customer.address || '';
        document.getElementById('customerPhone').value = customer.phone || '';
        document.getElementById('customerContact').value = customer.contact || '';
        document.getElementById('customerCountry').value = customer.country || '';
        
        customerModal.show();
    } catch (error) {
        console.error('加载客户信息失败:', error);
        showToast('error', '加载客户信息失败');
    }
}

// 保存客户信息
async function saveCustomer() {
    const customerId = document.getElementById('customerId').value;
    const customerData = {
        name: document.getElementById('customerName').value.trim(),
        address: document.getElementById('customerAddress').value.trim(),
        phone: document.getElementById('customerPhone').value.trim(),
        contact: document.getElementById('customerContact').value.trim(),
        country: document.getElementById('customerCountry').value
    };
    
    // 验证所有必填字段
    const required_fields = {
        'name': '客户名称',
        'address': '地址',
        'phone': '电话',
        'contact': '联系人',
        'country': '国家'
    };
    
    for (const [field, fieldName] of Object.entries(required_fields)) {
        if (!customerData[field]) {
            showToast('warning', `请输入${fieldName}`);
            return;
        }
    }
    
    try {
        const url = isEditing ? `/customer/${customerId}` : '/customer/add';
        const method = isEditing ? 'PUT' : 'POST';
        
        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(customerData)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            showToast('success', isEditing ? '客户信息已更新' : '客户已添加');
            customerModal.hide();
            loadCustomers();
        } else {
            showToast('error', result.error || '操作失败');
        }
    } catch (error) {
        console.error('保存客户信息失败:', error);
        showToast('error', '保存失败，请重试');
    }
}

// 删除客户
async function deleteCustomer(customerId) {
    if (!confirm('确定要删除这个客户吗？')) {
        return;
    }
    
    try {
        const response = await fetch(`/customer/${customerId}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (response.ok) {
            showToast('success', '客户已删除');
            loadCustomers();
        } else {
            showToast('error', result.error || '删除失败');
        }
    } catch (error) {
        console.error('删除客户失败:', error);
        showToast('error', '删除失败，请重试');
    }
}
</script>
{% endblock %} 