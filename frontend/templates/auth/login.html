{% extends 'auth/base.html' %}

{% block title %}登录{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">用户登录</h4>
            </div>
            <div class="card-body">
                <form id="login-form" novalidate>
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    
                    <div class="mb-3">
                        <label for="username" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="alert alert-danger d-none" id="login-error"></div>
                    
                    <button type="submit" class="btn btn-primary w-100" id="login-btn">
                        登录
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='auth/js/login.js') }}"></script>
{% endblock %} 