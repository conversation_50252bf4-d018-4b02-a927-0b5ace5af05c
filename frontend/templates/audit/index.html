{% extends "base.html" %}

{% block title %}操作审计系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>操作日志</h2>
        <div class="d-flex gap-2">
            <button class="btn btn-success" onclick="exportLogs()">
                <i class="bi bi-download"></i> 导出Excel
            </button>
            <a href="/" class="btn btn-outline-primary">
                <i class="bi bi-house-door"></i> 返回首页
            </a>
        </div>
    </div>

    <!-- 添加筛选表单 -->
    <div class="card mb-4">
        <div class="card-body">
            <form id="filterForm" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">用户</label>
                    <select class="form-select" id="userFilter">
                        <option value="">全部用户</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">操作类型</label>
                    <select class="form-select" id="actionFilter">
                        <option value="">全部操作</option>
                    </select>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i> 筛选
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>时间</th>
                    <th>用户</th>
                    <th>操作类型</th>
                    <th>详情</th>
                    <th>IP地址</th>
                </tr>
            </thead>
            <tbody id="logList"></tbody>
        </table>
        
        <!-- 分页控件 -->
        <nav aria-label="Page navigation" class="mt-3">
            <ul class="pagination justify-content-center" id="pagination"></ul>
        </nav>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
<style>
    .table {
        background-color: #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
    }
    .table thead th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        color: #2c3e50;
        font-weight: 600;
    }
    .table td {
        vertical-align: middle;
    }
    .pagination {
        margin-bottom: 0;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
const perPage = 100;

// 页面加载时获取日志列表和筛选选项
document.addEventListener('DOMContentLoaded', async () => {
    await loadFilterOptions();
    loadLogs(1);
    
    // 添加筛选表单提交事件
    document.getElementById('filterForm').addEventListener('submit', function(e) {
        e.preventDefault();
        loadLogs(1);
    });
});

// 加载筛选选项
async function loadFilterOptions() {
    try {
        const response = await fetch('/audit/filter-options');
        const data = await response.json();
        
        // 填充用户选项
        const userSelect = document.getElementById('userFilter');
        data.users.forEach(user => {
            const option = document.createElement('option');
            option.value = user.username;
            option.textContent = user.username;
            userSelect.appendChild(option);
        });
        
        // 填充操作类型选项
        const actionSelect = document.getElementById('actionFilter');
        data.action_types.forEach(action => {
            const option = document.createElement('option');
            option.value = action;
            option.textContent = action;
            actionSelect.appendChild(option);
        });
    } catch (error) {
        console.error('加载筛选选项失败:', error);
        showToast('error', '加载筛选选项失败');
    }
}

// 加载日志列表
async function loadLogs(page) {
    try {
        const userFilter = document.getElementById('userFilter').value;
        const actionFilter = document.getElementById('actionFilter').value;
        
        const url = new URL('/audit/logs', window.location.origin);
        url.searchParams.append('page', page);
        url.searchParams.append('per_page', perPage);
        if (userFilter) url.searchParams.append('username', userFilter);
        if (actionFilter) url.searchParams.append('action_type', actionFilter);
        
        const response = await fetch(url);
        const data = await response.json();
        
        const tbody = document.getElementById('logList');
        tbody.innerHTML = '';
        
        if (data.logs.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center">暂无日志记录</td></tr>';
            return;
        }
        
        data.logs.forEach(log => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${log.timestamp}</td>
                <td>${log.username}</td>
                <td>${log.action_type}</td>
                <td>${log.details}</td>
                <td>${log.ip}</td>
            `;
            tbody.appendChild(tr);
        });
        
        // 更新分页
        updatePagination(data.current_page, data.pages);
        currentPage = page;
    } catch (error) {
        console.error('加载日志列表失败:', error);
        showToast('error', '加载日志列表失败');
    }
}

// 更新分页控件
function updatePagination(currentPage, totalPages) {
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';
    
    // 上一页
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `
        <a class="page-link" href="#" onclick="loadLogs(${currentPage - 1})" ${currentPage === 1 ? 'tabindex="-1"' : ''}>
            上一页
        </a>
    `;
    pagination.appendChild(prevLi);
    
    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (
            i === 1 || // 第一页
            i === totalPages || // 最后一页
            (i >= currentPage - 2 && i <= currentPage + 2) // 当前页附近的页码
        ) {
            const li = document.createElement('li');
            li.className = `page-item ${i === currentPage ? 'active' : ''}`;
            li.innerHTML = `
                <a class="page-link" href="#" onclick="loadLogs(${i})">${i}</a>
            `;
            pagination.appendChild(li);
        } else if (
            (i === currentPage - 3 && currentPage > 4) ||
            (i === currentPage + 3 && currentPage < totalPages - 3)
        ) {
            // 添加省略号
            const li = document.createElement('li');
            li.className = 'page-item disabled';
            li.innerHTML = '<span class="page-link">...</span>';
            pagination.appendChild(li);
        }
    }
    
    // 下一页
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `
        <a class="page-link" href="#" onclick="loadLogs(${currentPage + 1})" ${currentPage === totalPages ? 'tabindex="-1"' : ''}>
            下一页
        </a>
    `;
    pagination.appendChild(nextLi);
}

// 导出日志
async function exportLogs() {
    try {
        const response = await fetch('/audit/export');
        if (!response.ok) {
            throw new Error('导出失败');
        }
        
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `操作日志_${new Date().toISOString().split('T')[0]}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
    } catch (error) {
        console.error('导出日志失败:', error);
        showToast('error', '导出日志失败');
    }
}
</script>
{% endblock %} 