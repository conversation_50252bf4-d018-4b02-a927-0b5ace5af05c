<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}系统主页{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/base.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/common.css') }}" rel="stylesheet">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 添加导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-grid-3x3-gap-fill me-2"></i>
                管理系统主页
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                <ul class="navbar-nav align-items-center">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle user-menu" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="d-flex align-items-center justify-content-between w-100">
                                <div class="user-info d-flex align-items-center justify-content-center flex-grow-1">
                                    <span class="username">{{ current_user.username }}</span>
                                    <span class="user-role ms-2 {% if current_user.is_admin %}admin-role{% endif %}">
                                        {% if current_user.is_admin %}管理员{% else %}用户{% endif %}
                                    </span>
                                </div>
                                <i class="bi bi-chevron-down dropdown-arrow"></i>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item logout-item" href="#" onclick="handleLogout()">
                                    <i class="bi bi-box-arrow-right"></i>
                                    <span>退出登录</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link login-link" href="{{ url_for('auth.login') }}">
                            <i class="bi bi-box-arrow-in-right me-1"></i>
                            登录
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% block content %}{% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/common.js') }}"></script>
    <script>
    function handleLogout() {
        fetch('/auth/logout', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.message === 'Logout successful') {
                window.location.href = '/auth/login';
            }
        })
        .catch(error => console.error('Logout failed:', error));
    }
    </script>
    {% block extra_js %}{% endblock %}

    <style>
    /* 导航栏基础样式 */
    .navbar {
        background: white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        padding: 0.8rem 0;
        position: sticky;
        top: 0;
        z-index: 1000;
    }

    /* 品牌标志样式 */
    .navbar-brand {
        color: #2c3e50;
        font-weight: 600;
        font-size: 1.2rem;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
    }

    .navbar-brand:hover {
        color: #4b6cb7;
        transform: translateY(-1px);
    }

    .navbar-brand i {
        font-size: 1.4rem;
        color: #4b6cb7;
        margin-right: 0.5rem;
    }

    /* 用户菜单样式 */
    .user-menu {
        padding: 0.5rem 1rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        color: #2c3e50;
        border: 1px solid transparent;
        min-width: 200px;
    }

    .user-menu:hover {
        background: rgba(75, 108, 183, 0.08);
        border-color: rgba(75, 108, 183, 0.1);
    }

    .user-menu::after {
        display: none !important;
    }

    .user-info {
        min-width: 0;
        padding-right: 0.5rem;
    }

    .dropdown-arrow {
        font-size: 0.85rem;
        color: #6c757d;
        transition: transform 0.3s ease;
    }

    .user-menu:hover .dropdown-arrow {
        transform: rotate(180deg);
    }

    .username {
        font-weight: 500;
        font-size: 0.95rem;
        color: #2c3e50;
    }

    .user-role {
        font-size: 0.95rem;
        padding: 0.2rem 0.8rem;
        border-radius: 6px;
        font-weight: 500;
        background: #f8f9fa;
        color: #6c757d;
        border: 1px solid #dee2e6;
        white-space: nowrap;
    }

    .admin-role {
        background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
        color: white;
        border: none;
    }

    /* 下拉菜单样式 */
    .dropdown-menu {
        border: none;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        border-radius: 8px;
        padding: 0.4rem;
        min-width: 200px;
        margin-top: 0.5rem;
    }

    .logout-item {
        padding: 0.7rem 1rem;
        color: #dc3545;
        display: flex;
        align-items: center;
        gap: 0.8rem;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    .logout-item:hover {
        background-color: rgba(220, 53, 69, 0.08);
        color: #dc3545;
    }

    .logout-item i {
        font-size: 1.1rem;
    }

    /* 登录链接样式 */
    .login-link {
        color: white;
        font-weight: 500;
        padding: 0.6rem 1.4rem;
        border-radius: 8px;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
        box-shadow: 0 2px 6px rgba(75, 108, 183, 0.2);
    }

    .login-link:hover {
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(75, 108, 183, 0.3);
    }

    .login-link:active {
        transform: translateY(1px);
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .navbar-brand {
            font-size: 1.1rem;
        }
        
        .user-menu {
            padding: 0.4rem 0.8rem;
            min-width: 180px;
        }
        
        .username, .user-role {
            font-size: 0.9rem;
        }

        .dropdown-menu {
            min-width: 180px;
        }
    }
    </style>
</body>
</html> 