{% extends "base.html" %}

{% block title %}供应商报价管理{% endblock %}

{% block extra_css %}
<style>
    .quote-card {
        border: none;
        border-radius: 12px;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        overflow: hidden;
        background: white;
    }

    .quote-card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }

    .quote-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1.5rem;
        color: white;
        position: relative;
    }

    .product-title {
        font-size: 1.4rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .product-brand {
        background: rgba(255, 255, 255, 0.2);
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }

    .quote-body {
        padding: 1.5rem;
        background: #fafbfc;
    }

    .supplier-section {
        background: white;
        border-radius: 8px;
        padding: 1.25rem;
        margin-bottom: 1rem;
        border-left: 4px solid #007bff;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .supplier-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .supplier-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #007bff, #0056b3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.1rem;
    }

    .supplier-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
    }

    .supplier-contact {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 0;
    }

    .quote-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .detail-item {
        text-align: center;
        padding: 0.75rem;
        background: white;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }

    .detail-label {
        font-size: 0.8rem;
        color: #6c757d;
        text-transform: uppercase;
        font-weight: 600;
        letter-spacing: 0.5px;
        margin-bottom: 0.25rem;
    }

    .detail-value {
        font-size: 1.1rem;
        font-weight: 700;
        color: #2c3e50;
    }

    .price-value {
        color: #28a745;
    }

    .my-price-value {
        color: #dc3545;
    }

    .customer-info {
        background: #e3f2fd;
        border-radius: 6px;
        padding: 0.75rem;
        margin-top: 1rem;
        border-left: 3px solid #2196f3;
    }

    .customer-label {
        font-size: 0.8rem;
        color: #1976d2;
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .customer-name {
        color: #0d47a1;
        font-weight: 600;
    }

    .quote-actions {
        padding: 1rem 1.5rem;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
        display: flex;
        justify-content: flex-end;
    }

    .status-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-ordered {
        background: rgba(40, 167, 69, 0.2);
        color: #155724;
        border: 1px solid rgba(40, 167, 69, 0.3);
    }

    .status-pending {
        background: rgba(108, 117, 125, 0.2);
        color: #495057;
        border: 1px solid rgba(108, 117, 125, 0.3);
    }

    .has-orders {
        border-top: 3px solid #28a745;
    }

    .no-orders {
        border-top: 3px solid #6c757d;
    }

    .filter-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 1.5rem;
        border-radius: 12px;
        margin-bottom: 2rem;
        border: 1px solid #dee2e6;
    }

    .inquiry-date {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }

    @media (max-width: 768px) {
        .quote-details {
            grid-template-columns: repeat(2, 1fr);
        }

        .product-title {
            font-size: 1.2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>供应商报价管理</h2>
            </div>

            <!-- 过滤器 -->
            <div class="filter-section">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">产品名称</label>
                        <select class="form-select" id="filterProductName" onchange="applyFilters()">
                            <!-- 产品选项将通过JavaScript动态填充 -->
                        </select>
                    </div>
                    <div class="col-md-8">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button class="btn btn-outline-secondary" onclick="clearFilters()">
                                <i class="bi bi-arrow-clockwise"></i> 显示全部
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 报价列表 -->
            <div id="quotesContainer">
                <!-- 报价卡片将在这里动态加载 -->
            </div>

            <!-- 分页 -->
            <nav aria-label="报价分页">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- 分页按钮将在这里动态生成 -->
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteQuoteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除这个供应商报价吗？</p>
                <div id="deleteQuoteDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmDeleteQuote()">删除</button>
            </div>
        </div>
    </div>
</div>

<script>
    let currentPage = 1;
    let currentQuoteId = null;
    let deleteModal = null;

    // 页面加载时获取报价列表和产品名称
    document.addEventListener('DOMContentLoaded', function () {
        console.log('DOM loaded, starting initialization...');
        loadProductNames().then(() => {
            console.log('Product names loaded, now loading quotes...');
            loadQuotes();
        }).catch(error => {
            console.error('Error loading product names:', error);
            loadQuotes(); // 即使产品名称加载失败，也要加载报价
        });
    });

    // 加载产品名称下拉菜单
    async function loadProductNames() {
        console.log('Loading product names...');
        try {
            const response = await fetch('/inquiry/product-names');
            const productNames = await response.json();
            console.log('Product names response:', productNames);

            if (response.ok) {
                const select = document.getElementById('filterProductName');
                // 添加产品名称选项
                productNames.forEach(name => {
                    const option = document.createElement('option');
                    option.value = name;
                    option.textContent = name;
                    select.appendChild(option);
                });
                console.log('Product names loaded successfully');
            } else {
                console.error('Failed to load product names:', productNames);
                showToast('error', '加载产品名称失败');
            }
        } catch (error) {
            console.error('加载产品名称失败:', error);
            showToast('error', '加载产品名称失败');
        }
    }

    // 加载报价列表
    async function loadQuotes(page = 1) {
        try {
            const productName = document.getElementById('filterProductName').value;

            const params = new URLSearchParams({
                page: page,
                per_page: 20
            });

            if (productName) params.append('product_name', productName);

            const response = await fetch(`/inquiry/supplier-quotes?${params}`);
            const data = await response.json();

            if (response.ok) {
                renderQuotes(data.quotes);
                renderPagination(data);
                currentPage = page;
            } else {
                showToast('error', data.error || '加载失败');
            }
        } catch (error) {
            console.error('加载报价列表失败:', error);
            showToast('error', '加载失败，请重试');
        }
    }

    // 渲染报价列表
    function renderQuotes(quotes) {
        const container = document.getElementById('quotesContainer');

        if (quotes.length === 0) {
            container.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-inbox display-1 text-muted"></i>
                <h4 class="text-muted mt-3">暂无报价记录</h4>
            </div>
        `;
            return;
        }

        container.innerHTML = quotes.map(quote => `
        <div class="quote-card ${quote.has_orders ? 'has-orders' : 'no-orders'}">
            <div class="quote-header">
                <div class="product-title">
                    <i class="bi bi-box-seam"></i>
                    ${quote.product_name}
                </div>
                ${quote.brand && quote.brand !== '无品牌' ? `
                    <div class="product-brand">${quote.brand}</div>
                ` : ''}
                <div class="status-badge ${quote.has_orders ? 'status-ordered' : 'status-pending'}">
                    ${quote.has_orders ? '已下单' : '未下单'}
                </div>
            </div>
            <div class="quote-body">
                <div class="supplier-section">
                    <div class="supplier-header">
                        <div class="supplier-icon">
                            <i class="bi bi-building"></i>
                        </div>
                        <div>
                            <div class="supplier-name">${quote.supplier_name}</div>
                            <div class="supplier-contact">${quote.supplier_contact || '无联系方式'}</div>
                        </div>
                    </div>

                    <div class="quote-details">
                        <div class="detail-item">
                            <div class="detail-label">数量</div>
                            <div class="detail-value">${quote.quantity}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">供应商报价</div>
                            <div class="detail-value price-value">¥${quote.price.toFixed(2)}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">我的价格</div>
                            <div class="detail-value my-price-value">¥${quote.my_price.toFixed(2)}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">交货周期</div>
                            <div class="detail-value">${quote.delivery_days} 天</div>
                        </div>
                    </div>

                    <div class="inquiry-date">
                        <i class="bi bi-calendar3"></i> 询价日期: ${new Date(quote.inquiry_date).toLocaleDateString()}
                    </div>
                </div>

                ${quote.customer_name ? `
                <div class="customer-info">
                    <div class="customer-label">
                        <i class="bi bi-person-circle"></i> 客户信息
                    </div>
                    <div class="customer-name">${quote.customer_name}</div>
                </div>
                ` : ''}
            </div>
            <div class="quote-actions">
                <button class="btn btn-sm btn-outline-danger ${quote.has_orders ? 'disabled' : ''}"
                        onclick="showDeleteQuote(${quote.id}, '${quote.product_name}', '${quote.supplier_name}')"
                        ${quote.has_orders ? 'disabled title="该报价已有关联订单，无法删除"' : ''}>
                    <i class="bi bi-trash"></i> 删除
                </button>
            </div>
        </div>
    `).join('');
    }

    // 渲染分页
    function renderPagination(data) {
        const pagination = document.getElementById('pagination');

        if (data.pages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHtml = '';

        // 上一页
        if (data.has_prev) {
            paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadQuotes(${data.current_page - 1})">上一页</a>
            </li>
        `;
        }

        // 页码
        for (let i = 1; i <= data.pages; i++) {
            if (i === data.current_page) {
                paginationHtml += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
            } else {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadQuotes(${i})">${i}</a></li>`;
            }
        }

        // 下一页
        if (data.has_next) {
            paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadQuotes(${data.current_page + 1})">下一页</a>
            </li>
        `;
        }

        pagination.innerHTML = paginationHtml;
    }

    // 显示删除确认对话框
    function showDeleteQuote(quoteId, productName, supplierName) {
        // 初始化 modal（如果还没有初始化）
        if (!deleteModal) {
            deleteModal = new bootstrap.Modal(document.getElementById('deleteQuoteModal'));
        }

        currentQuoteId = quoteId;
        document.getElementById('deleteQuoteDetails').innerHTML = `
        <strong>产品:</strong> ${productName}<br>
        <strong>供应商:</strong> ${supplierName}
    `;
        deleteModal.show();
    }

    // 确认删除报价
    async function confirmDeleteQuote() {
        if (!currentQuoteId) return;

        try {
            const response = await fetch(`/inquiry/supplier-quote/${currentQuoteId}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (response.ok) {
                showToast('success', '报价删除成功');
                if (deleteModal) {
                    deleteModal.hide();
                }
                loadQuotes(currentPage); // 重新加载当前页
            } else {
                showToast('error', result.error || '删除失败');
            }
        } catch (error) {
            console.error('删除报价失败:', error);
            showToast('error', '删除失败，请重试');
        }
    }

    // 应用过滤器
    function applyFilters() {
        loadQuotes(1);
    }

    // 清除过滤器
    function clearFilters() {
        document.getElementById('filterProductName').value = '';
        loadQuotes(1);
    }



    // Toast 提示函数
    function showToast(type, message) {
        // 这里可以使用现有的 toast 函数，或者简单的 alert
        if (typeof window.showToast === 'function') {
            window.showToast(type, message);
        } else {
            alert(message);
        }
    }
</script>
{% endblock %}