# Inquiry System Environment Configuration (v2.0)
# Copy this file to .env and update the values for your deployment

# ======================
# Application Environment
# ======================
FLASK_ENV=development
FLASK_DEBUG=1
FLASK_APP=inquiry_system.app:create_app

# ======================
# Security Configuration
# ======================
# Generate a secure secret key for production
# You can generate one using: python -c "import secrets; print(secrets.token_urlsafe(32))"
SECRET_KEY=your-super-secure-secret-key-here
WTF_CSRF_SECRET_KEY=your-csrf-secret-key-here

# ======================
# Database Configuration
# ======================
# Development (SQLite)
DEV_DATABASE_URL=sqlite:///instance/inquiry_dev.db

# Production (PostgreSQL)
DATABASE_URL=postgresql://username:password@localhost:5432/inquiry_prod

# Test Database
TEST_DATABASE_URL=sqlite:///:memory:

# Redis Configuration (for caching and rate limiting)
REDIS_URL=redis://localhost:6379/0

# ======================
# Application Configuration
# ======================
# Enable/disable automatic database backups
BACKUP_ENABLED=true

# Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Instance directory path
INSTANCE_PATH=/app/instance

# ======================
# Docker Configuration
# ======================
# User and group IDs for file permissions (development only)
UID=1000
GID=1000

# ======================
# Optional: External Services
# ======================
# Redis for session storage and caching
# REDIS_URL=redis://redis:6379/0

# Email configuration for notifications
# MAIL_SERVER=smtp.gmail.com
# MAIL_PORT=587
# MAIL_USE_TLS=true
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-app-password

# ======================
# Monitoring and Health Checks
# ======================
# Health check interval (seconds)
HEALTH_CHECK_INTERVAL=30

# Health check timeout (seconds)
HEALTH_CHECK_TIMEOUT=10

# ======================
# File Upload Configuration
# ======================
# Maximum file size for uploads (in bytes)
MAX_CONTENT_LENGTH=16777216  # 16MB

# Allowed file extensions
ALLOWED_EXTENSIONS=pdf,doc,docx,xls,xlsx,png,jpg,jpeg,gif
