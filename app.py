"""
Main application entry point for the Inquiry System

"""

import os
import sys
from pathlib import Path

# Add the backend/src directory to Python path
backend_src = Path(__file__).parent / "backend" / "src"
sys.path.insert(0, str(backend_src))

from inquiry_system.core import create_app

# Create the application
app = create_app()

# CLI commands
@app.cli.command('init-db')
def init_db_command():
    """Initialize the database."""
    from inquiry_system.core.database import db
    from inquiry_system.models.user import User
    
    print('Initializing database...')
    
    # Create all tables
    db.create_all()
    
    # Create default admin user if it doesn't exist
    admin_user = User.query.filter_by(username='admin').first()
    if not admin_user:
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            is_admin=True
        )
        admin_user.set_password('admin123')  # Change this in production!
        
        db.session.add(admin_user)
        db.session.commit()
        
        print('Default admin user created:')
        print('  Username: admin')
        print('  Password: admin123')
        print('  Please change the password after first login!')
    
    print('Database initialized successfully!')

if __name__ == '__main__':
    # Run the development server
    app.run(
        host='0.0.0.0',
        port=5123,
        debug=True
    )
