[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "inquiry-system"
version = "2.0.0"
description = "A modern Flask application for product inquiry management"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Inquiry System Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Inquiry System Team", email = "<EMAIL>"}
]
keywords = [
    "flask",
    "product-inquiry", 
    "inventory",
    "quotes",
    "serial-numbers",
    "supplier-management"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Environment :: Web Environment",
    "Framework :: Flask",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.9"
dependencies = [
    # Core Flask dependencies
    "Flask>=3.0.0,<4.0.0",
    "Flask-SQLAlchemy>=3.1.1,<4.0.0",
    "Flask-Migrate>=4.0.5,<5.0.0",
    "Flask-Login>=0.6.3,<1.0.0",
    "Flask-WTF>=1.2.1,<2.0.0",
    "Flask-CORS>=4.0.0,<5.0.0",
    
    # Database
    "SQLAlchemy>=2.0.23,<3.0.0",
    "alembic>=1.12.1,<2.0.0",
    "psycopg2-binary>=2.9.7,<3.0.0",  # PostgreSQL adapter
    
    # Security
    "Werkzeug>=3.0.1,<4.0.0",
    "cryptography>=41.0.0,<42.0.0",
    
    # Utilities
    "python-dateutil>=2.8.2,<3.0.0",
    "requests>=2.31.0,<3.0.0",
    "beautifulsoup4>=4.12.2,<5.0.0",
    "num2words>=0.5.13,<1.0.0",
    
    # File processing
    "Pillow>=10.1.0,<11.0.0",
    "openpyxl>=3.1.2,<4.0.0",
    "XlsxWriter>=3.1.9,<4.0.0",
    
    # Background tasks
    "APScheduler>=3.11.0,<4.0.0",
    "celery>=5.3.0,<6.0.0",
    "redis>=5.0.0,<6.0.0",
    
    # Environment management
    "python-dotenv>=1.0.0,<2.0.0",
    
    # Caching
    "Flask-Caching>=2.1.0,<3.0.0",
    
    # Rate limiting
    "Flask-Limiter>=3.5.0,<4.0.0",
    
    # Validation
    "marshmallow>=3.20.0,<4.0.0",
    "marshmallow-sqlalchemy>=0.29.0,<1.0.0",
    "email-validator>=2.1.0,<3.0.0",
    
    # Monitoring and logging
    "structlog>=23.2.0,<24.0.0",
    "prometheus-flask-exporter>=0.23.0,<1.0.0",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.0,<8.0.0",
    "pytest-cov>=4.1.0,<5.0.0",
    "pytest-flask>=1.3.0,<2.0.0",
    "pytest-mock>=3.12.0,<4.0.0",
    "factory-boy>=3.3.0,<4.0.0",
    "faker>=20.0.0,<21.0.0",
    
    # Code quality
    "black>=23.0.0,<24.0.0",
    "isort>=5.12.0,<6.0.0",
    "flake8>=6.1.0,<7.0.0",
    "mypy>=1.7.0,<2.0.0",
    "bandit>=1.7.5,<2.0.0",
    "safety>=2.3.0,<3.0.0",
    
    # Pre-commit hooks
    "pre-commit>=3.5.0,<4.0.0",
    
    # Development tools
    "Flask-DebugToolbar>=0.14.1,<1.0.0",
    "watchdog>=3.0.0,<4.0.0",
    
    # Documentation
    "sphinx>=7.2.0,<8.0.0",
    "sphinx-rtd-theme>=1.3.0,<2.0.0",
    "sphinx-autodoc-typehints>=1.25.0,<2.0.0",
]

prod = [
    # Production WSGI server
    "gunicorn>=21.2.0,<22.0.0",
    
    # Production monitoring
    "sentry-sdk[flask]>=1.38.0,<2.0.0",
    
    # Health checks
    "healthcheck>=1.3.3,<2.0.0",
]

test = [
    "pytest>=7.4.0,<8.0.0",
    "pytest-cov>=4.1.0,<5.0.0",
    "pytest-flask>=1.3.0,<2.0.0",
    "pytest-mock>=3.12.0,<4.0.0",
    "factory-boy>=3.3.0,<4.0.0",
    "faker>=20.0.0,<21.0.0",
]

[project.urls]
Homepage = "https://github.com/inquiry-system/inquiry-system"
Documentation = "https://inquiry-system.readthedocs.io/"
Repository = "https://github.com/inquiry-system/inquiry-system.git"
"Bug Tracker" = "https://github.com/inquiry-system/inquiry-system/issues"

[project.scripts]
inquiry-system = "inquiry_system.cli:main"

[tool.setuptools.packages.find]
where = ["backend/src"]

[tool.setuptools.package-dir]
"" = "backend/src"

# Black configuration
[tool.black]
line-length = 88
target-version = ['py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["inquiry_system"]
known_third_party = ["flask", "sqlalchemy", "marshmallow"]

# MyPy configuration
[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "flask_sqlalchemy.*",
    "flask_migrate.*",
    "flask_login.*",
    "flask_wtf.*",
    "flask_cors.*",
    "openpyxl.*",
    "num2words.*",
    "apscheduler.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

# Coverage configuration
[tool.coverage.run]
source = ["backend/src/inquiry_system"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/venv/*",
    "*/env/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.coverage.html]
directory = "htmlcov"

# Bandit configuration
[tool.bandit]
exclude_dirs = ["tests", "migrations"]
skips = ["B101", "B601"]
